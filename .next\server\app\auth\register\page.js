/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/register/page";
exports.ids = ["app/auth/register/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fregister%2Fpage&page=%2Fauth%2Fregister%2Fpage&appPaths=%2Fauth%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fregister%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fregister%2Fpage&page=%2Fauth%2Fregister%2Fpage&appPaths=%2Fauth%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fregister%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'register',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/auth/register/page.tsx */ \"(rsc)/./app/auth/register/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\auth\\\\register\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\auth\\\\register\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth/register/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/register/page\",\n        pathname: \"/auth/register\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fregister%2Fpage&page=%2Fauth%2Fregister%2Fpage&appPaths=%2Fauth%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fregister%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cperformance%5CPerformanceOptimization.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Csecurity%5CSecurityAccessibility.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cperformance%5CPerformanceOptimization.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Csecurity%5CSecurityAccessibility.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/performance/PerformanceOptimization.tsx */ \"(ssr)/./components/performance/PerformanceOptimization.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/security/SecurityAccessibility.tsx */ \"(ssr)/./components/security/SecurityAccessibility.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/AuthContext.tsx */ \"(ssr)/./contexts/AuthContext.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcGVlYnMlNUNEb2N1bWVudHMlNUNwcm9qZWN0cyU1Q3A3LWNvbXByZWhlbnNpdmUlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3BlZWJzJTVDRG9jdW1lbnRzJTVDcHJvamVjdHMlNUNwNy1jb21wcmVoZW5zaXZlJTVDY29tcG9uZW50cyU1Q3BlcmZvcm1hbmNlJTVDUGVyZm9ybWFuY2VPcHRpbWl6YXRpb24udHN4Jm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcGVlYnMlNUNEb2N1bWVudHMlNUNwcm9qZWN0cyU1Q3A3LWNvbXByZWhlbnNpdmUlNUNjb21wb25lbnRzJTVDc2VjdXJpdHklNUNTZWN1cml0eUFjY2Vzc2liaWxpdHkudHN4Jm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcGVlYnMlNUNEb2N1bWVudHMlNUNwcm9qZWN0cyU1Q3A3LWNvbXByZWhlbnNpdmUlNUNjb250ZXh0cyU1Q0F1dGhDb250ZXh0LnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3BlZWJzJTVDRG9jdW1lbnRzJTVDcHJvamVjdHMlNUNwNy1jb21wcmVoZW5zaXZlJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNzY3JpcHQuanMmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNwZWVicyU1Q0RvY3VtZW50cyU1Q3Byb2plY3RzJTVDcDctY29tcHJlaGVuc2l2ZSU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlMkMlMjJ2YXJpYWJsZSUyMiUzQSUyMi0tZm9udC1pbnRlciUyMiUyQyUyMmRpc3BsYXklMjIlM0ElMjJzd2FwJTIyJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNwZWVicyU1Q0RvY3VtZW50cyU1Q3Byb2plY3RzJTVDcDctY29tcHJlaGVuc2l2ZSU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIyUG9wcGlucyUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCUyQyUyMndlaWdodCUyMiUzQSU1QiUyMjMwMCUyMiUyQyUyMjQwMCUyMiUyQyUyMjUwMCUyMiUyQyUyMjYwMCUyMiUyQyUyMjcwMCUyMiUyQyUyMjgwMCUyMiU1RCUyQyUyMnZhcmlhYmxlJTIyJTNBJTIyLS1mb250LXBvcHBpbnMlMjIlMkMlMjJkaXNwbGF5JTIyJTNBJTIyc3dhcCUyMiU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMnBvcHBpbnMlMjIlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9OQUFrSjtBQUNsSiwwTUFBNkk7QUFDN0ksZ0tBQXVIO0FBQ3ZIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9zaXRpdmU3LXRvdXJpc20td2Vic2l0ZS8/MTg0ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHBlZWJzXFxcXERvY3VtZW50c1xcXFxwcm9qZWN0c1xcXFxwNy1jb21wcmVoZW5zaXZlXFxcXGNvbXBvbmVudHNcXFxccGVyZm9ybWFuY2VcXFxcUGVyZm9ybWFuY2VPcHRpbWl6YXRpb24udHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxwZWVic1xcXFxEb2N1bWVudHNcXFxccHJvamVjdHNcXFxccDctY29tcHJlaGVuc2l2ZVxcXFxjb21wb25lbnRzXFxcXHNlY3VyaXR5XFxcXFNlY3VyaXR5QWNjZXNzaWJpbGl0eS50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHBlZWJzXFxcXERvY3VtZW50c1xcXFxwcm9qZWN0c1xcXFxwNy1jb21wcmVoZW5zaXZlXFxcXGNvbnRleHRzXFxcXEF1dGhDb250ZXh0LnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccGVlYnNcXFxcRG9jdW1lbnRzXFxcXHByb2plY3RzXFxcXHA3LWNvbXByZWhlbnNpdmVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcc2NyaXB0LmpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cperformance%5CPerformanceOptimization.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Csecurity%5CSecurityAccessibility.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cauth%5CRegisterForm.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cui%5CLoadingSpinner.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cauth%5CRegisterForm.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cui%5CLoadingSpinner.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth/RegisterForm.tsx */ \"(ssr)/./components/auth/RegisterForm.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/LoadingSpinner.tsx */ \"(ssr)/./components/ui/LoadingSpinner.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcGVlYnMlNUNEb2N1bWVudHMlNUNwcm9qZWN0cyU1Q3A3LWNvbXByZWhlbnNpdmUlNUNjb21wb25lbnRzJTVDYXV0aCU1Q1JlZ2lzdGVyRm9ybS50c3gmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNwZWVicyU1Q0RvY3VtZW50cyU1Q3Byb2plY3RzJTVDcDctY29tcHJlaGVuc2l2ZSU1Q2NvbXBvbmVudHMlNUN1aSU1Q0xvYWRpbmdTcGlubmVyLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0xBQWdJO0FBQ2hJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9zaXRpdmU3LXRvdXJpc20td2Vic2l0ZS8/YmY2NiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHBlZWJzXFxcXERvY3VtZW50c1xcXFxwcm9qZWN0c1xcXFxwNy1jb21wcmVoZW5zaXZlXFxcXGNvbXBvbmVudHNcXFxcYXV0aFxcXFxSZWdpc3RlckZvcm0udHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxwZWVic1xcXFxEb2N1bWVudHNcXFxccHJvamVjdHNcXFxccDctY29tcHJlaGVuc2l2ZVxcXFxjb21wb25lbnRzXFxcXHVpXFxcXExvYWRpbmdTcGlubmVyLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cauth%5CRegisterForm.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cui%5CLoadingSpinner.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcGVlYnMlNUNEb2N1bWVudHMlNUNwcm9qZWN0cyU1Q3A3LWNvbXByZWhlbnNpdmUlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNhcHAtcm91dGVyLmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcGVlYnMlNUNEb2N1bWVudHMlNUNwcm9qZWN0cyU1Q3A3LWNvbXByZWhlbnNpdmUlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNlcnJvci1ib3VuZGFyeS5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3BlZWJzJTVDRG9jdW1lbnRzJTVDcHJvamVjdHMlNUNwNy1jb21wcmVoZW5zaXZlJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3BlZWJzJTVDRG9jdW1lbnRzJTVDcHJvamVjdHMlNUNwNy1jb21wcmVoZW5zaXZlJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbm90LWZvdW5kLWJvdW5kYXJ5LmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcGVlYnMlNUNEb2N1bWVudHMlNUNwcm9qZWN0cyU1Q3A3LWNvbXByZWhlbnNpdmUlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcGVlYnMlNUNEb2N1bWVudHMlNUNwcm9qZWN0cyU1Q3A3LWNvbXByZWhlbnNpdmUlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQXlKO0FBQ3pKLDBPQUE2SjtBQUM3Six3T0FBNEo7QUFDNUosa1BBQWlLO0FBQ2pLLHNRQUEySztBQUMzSyIsInNvdXJjZXMiOlsid2VicGFjazovL3Bvc2l0aXZlNy10b3VyaXNtLXdlYnNpdGUvPzg1MzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxwZWVic1xcXFxEb2N1bWVudHNcXFxccHJvamVjdHNcXFxccDctY29tcHJlaGVuc2l2ZVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGFwcC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHBlZWJzXFxcXERvY3VtZW50c1xcXFxwcm9qZWN0c1xcXFxwNy1jb21wcmVoZW5zaXZlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHBlZWJzXFxcXERvY3VtZW50c1xcXFxwcm9qZWN0c1xcXFxwNy1jb21wcmVoZW5zaXZlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccGVlYnNcXFxcRG9jdW1lbnRzXFxcXHByb2plY3RzXFxcXHA3LWNvbXByZWhlbnNpdmVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHBlZWJzXFxcXERvY3VtZW50c1xcXFxwcm9qZWN0c1xcXFxwNy1jb21wcmVoZW5zaXZlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccGVlYnNcXFxcRG9jdW1lbnRzXFxcXHByb2plY3RzXFxcXHA3LWNvbXByZWhlbnNpdmVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/auth/RegisterForm.tsx":
/*!******************************************!*\
  !*** ./components/auth/RegisterForm.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RegisterForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Loader2,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Loader2,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Loader2,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Loader2,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Loader2,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Loader2,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Loader2,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Loader2,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Loader2,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Loader2,Lock,Mail,Phone,User,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction RegisterForm() {\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        fullName: \"\",\n        email: \"\",\n        phone: \"\",\n        password: \"\",\n        confirmPassword: \"\"\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { signUp, signInWithGoogle } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        setError(\"\") // Clear error when user starts typing\n        ;\n    };\n    const validateForm = ()=>{\n        if (!formData.fullName.trim()) {\n            setError(\"Full name is required\");\n            return false;\n        }\n        if (!formData.email.trim()) {\n            setError(\"Email is required\");\n            return false;\n        }\n        if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n            setError(\"Please enter a valid email address\");\n            return false;\n        }\n        if (!formData.phone.trim()) {\n            setError(\"Phone number is required\");\n            return false;\n        }\n        if (!/^[+]?[\\d\\s-()]{10,}$/.test(formData.phone)) {\n            setError(\"Please enter a valid phone number\");\n            return false;\n        }\n        if (formData.password.length < 8) {\n            setError(\"Password must be at least 8 characters long\");\n            return false;\n        }\n        if (formData.password !== formData.confirmPassword) {\n            setError(\"Passwords do not match\");\n            return false;\n        }\n        return true;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setLoading(true);\n        setError(\"\");\n        try {\n            const { error } = await signUp(formData.email, formData.password, {\n                full_name: formData.fullName,\n                phone: formData.phone\n            });\n            if (error) {\n                setError(error);\n                return;\n            }\n            setSuccess(true);\n            // Announce success to screen readers\n            if (false) {}\n            // Redirect after a short delay to show success message\n            setTimeout(()=>{\n                router.push(\"/auth/login?message=Please check your email to verify your account\");\n            }, 2000);\n        } catch (err) {\n            console.error(\"Registration error:\", err);\n            setError(\"An unexpected error occurred. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (success) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n            initial: {\n                opacity: 0,\n                scale: 0.95\n            },\n            animate: {\n                opacity: 1,\n                scale: 1\n            },\n            className: \"bg-white rounded-2xl shadow-xl p-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-8 h-8 text-green-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                    children: \"Account Created!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-4\",\n                    children: \"Please check your email to verify your account before signing in.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/auth/login\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"bg-gradient-to-r from-blue-600 to-green-600\",\n                        children: \"Go to Login\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.5\n        },\n        className: \"bg-white rounded-2xl shadow-xl p-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            src: \"/images/positive7-logo.png\",\n                            alt: \"Positive7 Educational Tours\",\n                            width: 80,\n                            height: 80,\n                            className: \"rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-2\",\n                        children: \"Create Account\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Join Positive7 for amazing educational tours\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.95\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center\",\n                role: \"alert\",\n                \"aria-live\": \"polite\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"w-5 h-5 text-red-500 mr-3 flex-shrink-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-red-700 text-sm\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"fullName\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Full Name\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        id: \"fullName\",\n                                        name: \"fullName\",\n                                        type: \"text\",\n                                        autoComplete: \"name\",\n                                        required: true,\n                                        value: formData.fullName,\n                                        onChange: (e)=>handleInputChange(\"fullName\", e.target.value),\n                                        className: \"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                        placeholder: \"Enter your full name\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"email\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Email Address\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        id: \"email\",\n                                        name: \"email\",\n                                        type: \"email\",\n                                        autoComplete: \"email\",\n                                        required: true,\n                                        value: formData.email,\n                                        onChange: (e)=>handleInputChange(\"email\", e.target.value),\n                                        className: \"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                        placeholder: \"Enter your email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"phone\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Phone Number\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        id: \"phone\",\n                                        name: \"phone\",\n                                        type: \"tel\",\n                                        autoComplete: \"tel\",\n                                        required: true,\n                                        value: formData.phone,\n                                        onChange: (e)=>handleInputChange(\"phone\", e.target.value),\n                                        className: \"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                        placeholder: \"Enter your phone number\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"password\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Password\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        id: \"password\",\n                                        name: \"password\",\n                                        type: showPassword ? \"text\" : \"password\",\n                                        autoComplete: \"new-password\",\n                                        required: true,\n                                        value: formData.password,\n                                        onChange: (e)=>handleInputChange(\"password\", e.target.value),\n                                        className: \"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                        placeholder: \"Create a password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowPassword(!showPassword),\n                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                        \"aria-label\": showPassword ? \"Hide password\" : \"Show password\",\n                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400 hover:text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400 hover:text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-xs text-gray-500\",\n                                children: \"Password must be at least 8 characters long\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"confirmPassword\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Confirm Password\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        id: \"confirmPassword\",\n                                        name: \"confirmPassword\",\n                                        type: showConfirmPassword ? \"text\" : \"password\",\n                                        autoComplete: \"new-password\",\n                                        required: true,\n                                        value: formData.confirmPassword,\n                                        onChange: (e)=>handleInputChange(\"confirmPassword\", e.target.value),\n                                        className: \"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                        placeholder: \"Confirm your password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                        \"aria-label\": showConfirmPassword ? \"Hide password\" : \"Show password\",\n                                        children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400 hover:text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400 hover:text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                id: \"terms\",\n                                name: \"terms\",\n                                type: \"checkbox\",\n                                required: true,\n                                className: \"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"terms\",\n                                className: \"ml-3 text-sm text-gray-600\",\n                                children: [\n                                    \"I agree to the\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/terms\",\n                                        className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                        children: \"Terms of Service\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, this),\n                                    \" \",\n                                    \"and\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        type: \"submit\",\n                        disabled: loading,\n                        className: \"w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this),\n                                \"Creating Account...\"\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Loader2_Lock_Mail_Phone_User_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this),\n                                \"Create Account\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 mb-6 hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full border-t border-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex justify-center text-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 bg-white text-gray-500\",\n                                children: \"Or continue with\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    variant: \"outline\",\n                    onClick: async ()=>{\n                        setLoading(true);\n                        setError(\"\");\n                        try {\n                            const { error } = await signInWithGoogle();\n                            if (error) {\n                                setError(error);\n                            }\n                        } catch (err) {\n                            console.error(\"Google registration error:\", err);\n                            setError(\"Failed to sign up with Google. Please try again.\");\n                        } finally{\n                            setLoading(false);\n                        }\n                    },\n                    disabled: loading,\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 mr-2\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fill: \"currentColor\",\n                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fill: \"currentColor\",\n                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fill: \"currentColor\",\n                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fill: \"currentColor\",\n                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, this),\n                        \"Continue with Google\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                    lineNumber: 362,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [\n                        \"Already have an account?\",\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/auth/login\",\n                            className: \"font-medium text-blue-600 hover:text-blue-700\",\n                            children: \"Sign in here\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n                lineNumber: 393,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\auth\\\\RegisterForm.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/auth/RegisterForm.tsx\n");

/***/ }),

/***/ "(ssr)/./components/performance/PerformanceOptimization.tsx":
/*!************************************************************!*\
  !*** ./components/performance/PerformanceOptimization.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BundleAnalyzer: () => (/* binding */ BundleAnalyzer),\n/* harmony export */   LazyLoadSection: () => (/* binding */ LazyLoadSection),\n/* harmony export */   MemoizedSearchResults: () => (/* binding */ MemoizedSearchResults),\n/* harmony export */   OptimizedAnimation: () => (/* binding */ OptimizedAnimation),\n/* harmony export */   OptimizedImage: () => (/* binding */ OptimizedImage),\n/* harmony export */   PerformanceBudget: () => (/* binding */ PerformanceBudget),\n/* harmony export */   VirtualScroll: () => (/* binding */ VirtualScroll),\n/* harmony export */   useDebounce: () => (/* binding */ useDebounce),\n/* harmony export */   useImagePreloader: () => (/* binding */ useImagePreloader),\n/* harmony export */   useLazyLoad: () => (/* binding */ useLazyLoad),\n/* harmony export */   usePerformanceMonitoring: () => (/* binding */ usePerformanceMonitoring)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/shared/lib/app-dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ OptimizedImage,useLazyLoad,VirtualScroll,useDebounce,MemoizedSearchResults,LazyLoadSection,usePerformanceMonitoring,OptimizedAnimation,useImagePreloader,BundleAnalyzer,PerformanceBudget auto */ \n\n\n\n\n// Simple intersection observer hook implementation\nfunction useIntersectionObserver(threshold = 0.1) {\n    const [isIntersecting, setIsIntersecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const observer = new IntersectionObserver(([entry])=>{\n            setIsIntersecting(entry.isIntersecting);\n        }, {\n            threshold\n        });\n        if (ref.current) {\n            observer.observe(ref.current);\n        }\n        return ()=>{\n            if (ref.current) {\n                observer.unobserve(ref.current);\n            }\n        };\n    }, [\n        threshold\n    ]);\n    return {\n        ref,\n        isIntersecting\n    };\n}\n// Lazy load heavy components\nconst LazyChart = next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx -> \" + \"recharts\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-64 bg-gray-200 animate-pulse rounded-lg\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n            lineNumber: 37,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\nfunction OptimizedImage({ src, alt, width, height, className = \"\", priority = false, sizes = \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\", fill = false }) {\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleLoad = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setIsLoading(false);\n    }, []);\n    const handleError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setError(true);\n        setIsLoading(false);\n    }, []);\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `bg-gray-200 flex items-center justify-center ${className}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-gray-500 text-sm\",\n                children: \"Image unavailable\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative overflow-hidden ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                src: src,\n                alt: alt,\n                width: fill ? undefined : width,\n                height: fill ? undefined : height,\n                fill: fill,\n                sizes: sizes,\n                priority: priority,\n                quality: 85,\n                placeholder: \"blur\",\n                blurDataURL: \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==\",\n                onLoad: handleLoad,\n                onError: handleError,\n                className: `transition-opacity duration-300 ${isLoading ? \"opacity-0\" : \"opacity-100\"} ${fill ? \"object-cover\" : \"\"}`\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gray-200 animate-pulse\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n// Intersection Observer Hook for lazy loading\nfunction useLazyLoad(threshold = 0.1) {\n    const { ref, isIntersecting } = useIntersectionObserver(threshold);\n    const [inView, setInView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isIntersecting && !inView) {\n            setInView(true) // Once in view, stay in view (triggerOnce behavior)\n            ;\n        }\n    }, [\n        isIntersecting,\n        inView\n    ]);\n    return {\n        ref,\n        inView\n    };\n}\nfunction VirtualScroll({ items, itemHeight, containerHeight, renderItem, overscan = 5 }) {\n    const [scrollTop, setScrollTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const visibleStart = Math.floor(scrollTop / itemHeight);\n    const visibleEnd = Math.min(visibleStart + Math.ceil(containerHeight / itemHeight), items.length - 1);\n    const paddingTop = visibleStart * itemHeight;\n    const paddingBottom = (items.length - visibleEnd - 1) * itemHeight;\n    const visibleItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return items.slice(Math.max(0, visibleStart - overscan), Math.min(items.length, visibleEnd + 1 + overscan));\n    }, [\n        items,\n        visibleStart,\n        visibleEnd,\n        overscan\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            height: containerHeight,\n            overflow: \"auto\"\n        },\n        onScroll: (e)=>setScrollTop(e.currentTarget.scrollTop),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                paddingTop,\n                paddingBottom\n            },\n            children: visibleItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        height: itemHeight\n                    },\n                    children: renderItem(item, visibleStart - overscan + index)\n                }, visibleStart - overscan + index, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n// Debounced Search Hook\nfunction useDebounce(value, delay) {\n    const [debouncedValue, setDebouncedValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handler = setTimeout(()=>{\n            setDebouncedValue(value);\n        }, delay);\n        return ()=>{\n            clearTimeout(handler);\n        };\n    }, [\n        value,\n        delay\n    ]);\n    return debouncedValue;\n}\nfunction MemoizedSearchResults({ query, items, searchFields, renderItem }) {\n    const debouncedQuery = useDebounce(query, 300);\n    const filteredItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!debouncedQuery.trim()) return items;\n        return items.filter((item)=>searchFields.some((field)=>item[field]?.toLowerCase().includes(debouncedQuery.toLowerCase())));\n    }, [\n        items,\n        debouncedQuery,\n        searchFields\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: filteredItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: renderItem(item)\n            }, item.id || index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n                lineNumber: 219,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\nfunction LazyLoadSection({ children, fallback = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n    className: \"h-64 bg-gray-200 animate-pulse rounded-lg\"\n}, void 0, false, {\n    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n    lineNumber: 237,\n    columnNumber: 14\n}, this), threshold = 0.1, className = \"\" }) {\n    const { ref, inView } = useLazyLoad(threshold);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: className,\n        children: inView ? children : fallback\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, this);\n}\n// Performance Monitoring Hook\nfunction usePerformanceMonitoring() {\n    const [metrics, setMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        loadTime: 0,\n        renderTime: 0,\n        memoryUsage: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Measure page load time\n        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;\n        // Measure render time\n        const renderStart = performance.now();\n        // Memory usage (if available)\n        const memoryUsage = performance.memory?.usedJSHeapSize || 0;\n        setTimeout(()=>{\n            const renderTime = performance.now() - renderStart;\n            setMetrics({\n                loadTime,\n                renderTime,\n                memoryUsage\n            });\n        }, 0);\n        // Report Core Web Vitals\n        try {\n            __webpack_require__.e(/*! import() */ \"vendor-chunks/web-vitals\").then(__webpack_require__.bind(__webpack_require__, /*! web-vitals */ \"(ssr)/./node_modules/web-vitals/dist/web-vitals.js\")).then(({ getCLS, getFID, getFCP, getLCP, getTTFB })=>{\n                getCLS(console.log);\n                getFID(console.log);\n                getFCP(console.log);\n                getLCP(console.log);\n                getTTFB(console.log);\n            }).catch(()=>{\n            // web-vitals not available, skip\n            });\n        } catch (error) {\n        // web-vitals not available, skip\n        }\n    }, []);\n    return metrics;\n}\nfunction OptimizedAnimation({ children, animation = \"fadeIn\", duration = 0.5, delay = 0, threshold = 0.1 }) {\n    const { ref, inView } = useLazyLoad(threshold);\n    const animations = {\n        fadeIn: {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            }\n        },\n        slideUp: {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            }\n        },\n        slideLeft: {\n            initial: {\n                opacity: 0,\n                x: 20\n            },\n            animate: {\n                opacity: 1,\n                x: 0\n            }\n        },\n        scale: {\n            initial: {\n                opacity: 0,\n                scale: 0.9\n            },\n            animate: {\n                opacity: 1,\n                scale: 1\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n            children: inView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                initial: animations[animation].initial,\n                animate: animations[animation].animate,\n                transition: {\n                    duration,\n                    delay\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n                lineNumber: 337,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n            lineNumber: 335,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n        lineNumber: 334,\n        columnNumber: 5\n    }, this);\n}\n// Image Preloader Hook\nfunction useImagePreloader(imageUrls) {\n    const [loadedImages, setLoadedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const preloadImages = async ()=>{\n            const promises = imageUrls.map((url)=>{\n                return new Promise((resolve, reject)=>{\n                    const img = new window.Image();\n                    img.onload = ()=>resolve(url);\n                    img.onerror = reject;\n                    img.src = url;\n                });\n            });\n            try {\n                const loaded = await Promise.allSettled(promises);\n                const successful = loaded.filter((result)=>result.status === \"fulfilled\").map((result)=>result.value);\n                setLoadedImages(new Set(successful));\n            } catch (error) {\n                console.error(\"Error preloading images:\", error);\n            }\n        };\n        if (imageUrls.length > 0) {\n            preloadImages();\n        }\n    }, [\n        imageUrls\n    ]);\n    return loadedImages;\n}\n// Bundle Size Analyzer (Development only)\nfunction BundleAnalyzer() {\n    if (false) {}\n    const [bundleInfo, setBundleInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This would integrate with webpack-bundle-analyzer in development\n        if (false) {}\n    }, []);\n    if (!bundleInfo) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 bg-black text-white p-4 rounded-lg text-xs\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"font-bold mb-2\",\n                children: \"Bundle Info\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    \"Total Size: \",\n                    bundleInfo.totalSize\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n                lineNumber: 405,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    \"Chunks: \",\n                    bundleInfo.chunks\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n                lineNumber: 406,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    \"Modules: \",\n                    bundleInfo.modules\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n                lineNumber: 407,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n        lineNumber: 403,\n        columnNumber: 5\n    }, this);\n}\n// Performance Budget Component\nfunction PerformanceBudget() {\n    const metrics = usePerformanceMonitoring();\n    const [warnings, setWarnings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const newWarnings = [];\n        if (metrics.loadTime > 3000) {\n            newWarnings.push(\"Page load time exceeds 3 seconds\");\n        }\n        if (metrics.memoryUsage > 50 * 1024 * 1024) {\n            newWarnings.push(\"Memory usage exceeds 50MB\");\n        }\n        setWarnings(newWarnings);\n    }, [\n        metrics\n    ]);\n    if ( false || warnings.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 bg-red-500 text-white p-4 rounded-lg text-sm max-w-xs\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"font-bold mb-2\",\n                children: \"Performance Warnings\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n                lineNumber: 437,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"space-y-1\",\n                children: warnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            \"• \",\n                            warning\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\performance\\\\PerformanceOptimization.tsx\",\n        lineNumber: 436,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3BlcmZvcm1hbmNlL1BlcmZvcm1hbmNlT3B0aW1pemF0aW9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXlFO0FBQ2xCO0FBQ3pCO0FBQ0k7QUFFbEMsbURBQW1EO0FBQ25ELFNBQVNTLHdCQUF3QkMsWUFBWSxHQUFHO0lBQzlDLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBR1osK0NBQVFBLENBQUM7SUFDckQsTUFBTWEsTUFBTVQsNkNBQU1BLENBQWlCO0lBRW5DSCxnREFBU0EsQ0FBQztRQUNSLE1BQU1hLFdBQVcsSUFBSUMscUJBQ25CLENBQUMsQ0FBQ0MsTUFBTTtZQUNOSixrQkFBa0JJLE1BQU1MLGNBQWM7UUFDeEMsR0FDQTtZQUFFRDtRQUFVO1FBR2QsSUFBSUcsSUFBSUksT0FBTyxFQUFFO1lBQ2ZILFNBQVNJLE9BQU8sQ0FBQ0wsSUFBSUksT0FBTztRQUM5QjtRQUVBLE9BQU87WUFDTCxJQUFJSixJQUFJSSxPQUFPLEVBQUU7Z0JBQ2ZILFNBQVNLLFNBQVMsQ0FBQ04sSUFBSUksT0FBTztZQUNoQztRQUNGO0lBQ0YsR0FBRztRQUFDUDtLQUFVO0lBRWQsT0FBTztRQUFFRztRQUFLRjtJQUFlO0FBQy9CO0FBRUEsNkJBQTZCO0FBQzdCLE1BQU1TLFlBQVlaLG1EQUFPQTs7Ozs7Ozs7SUFDdkJhLFNBQVMsa0JBQU0sOERBQUNDO1lBQUlDLFdBQVU7Ozs7OztJQUM5QkMsS0FBSzs7QUFlQSxTQUFTQyxlQUFlLEVBQzdCQyxHQUFHLEVBQ0hDLEdBQUcsRUFDSEMsS0FBSyxFQUNMQyxNQUFNLEVBQ05OLFlBQVksRUFBRSxFQUNkTyxXQUFXLEtBQUssRUFDaEJDLFFBQVEsMERBQTBELEVBQ2xFQyxPQUFPLEtBQUssRUFDUTtJQUNwQixNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR2xDLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ21DLE9BQU9DLFNBQVMsR0FBR3BDLCtDQUFRQSxDQUFDO0lBRW5DLE1BQU1xQyxhQUFhbkMsa0RBQVdBLENBQUM7UUFDN0JnQyxhQUFhO0lBQ2YsR0FBRyxFQUFFO0lBRUwsTUFBTUksY0FBY3BDLGtEQUFXQSxDQUFDO1FBQzlCa0MsU0FBUztRQUNURixhQUFhO0lBQ2YsR0FBRyxFQUFFO0lBRUwsSUFBSUMsT0FBTztRQUNULHFCQUNFLDhEQUFDYjtZQUFJQyxXQUFXLENBQUMsNkNBQTZDLEVBQUVBLFVBQVUsQ0FBQztzQkFDekUsNEVBQUNnQjtnQkFBS2hCLFdBQVU7MEJBQXdCOzs7Ozs7Ozs7OztJQUc5QztJQUVBLHFCQUNFLDhEQUFDRDtRQUFJQyxXQUFXLENBQUMseUJBQXlCLEVBQUVBLFVBQVUsQ0FBQzs7MEJBQ3JELDhEQUFDaEIsbURBQUtBO2dCQUNKbUIsS0FBS0E7Z0JBQ0xDLEtBQUtBO2dCQUNMQyxPQUFPSSxPQUFPUSxZQUFZWjtnQkFDMUJDLFFBQVFHLE9BQU9RLFlBQVlYO2dCQUMzQkcsTUFBTUE7Z0JBQ05ELE9BQU9BO2dCQUNQRCxVQUFVQTtnQkFDVlcsU0FBUztnQkFDVEMsYUFBWTtnQkFDWkMsYUFBWTtnQkFDWkMsUUFBUVA7Z0JBQ1JRLFNBQVNQO2dCQUNUZixXQUFXLENBQUMsZ0NBQWdDLEVBQzFDVSxZQUFZLGNBQWMsY0FDM0IsQ0FBQyxFQUFFRCxPQUFPLGlCQUFpQixHQUFHLENBQUM7Ozs7OztZQUVqQ0MsMkJBQ0MsOERBQUNYO2dCQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7QUFJdkI7QUFFQSw4Q0FBOEM7QUFDdkMsU0FBU3VCLFlBQVlwQyxZQUFZLEdBQUc7SUFDekMsTUFBTSxFQUFFRyxHQUFHLEVBQUVGLGNBQWMsRUFBRSxHQUFHRix3QkFBd0JDO0lBQ3hELE1BQU0sQ0FBQ3FDLFFBQVFDLFVBQVUsR0FBR2hELCtDQUFRQSxDQUFDO0lBRXJDQyxnREFBU0EsQ0FBQztRQUNSLElBQUlVLGtCQUFrQixDQUFDb0MsUUFBUTtZQUM3QkMsVUFBVSxNQUFNLG9EQUFvRDs7UUFDdEU7SUFDRixHQUFHO1FBQUNyQztRQUFnQm9DO0tBQU87SUFFM0IsT0FBTztRQUFFbEM7UUFBS2tDO0lBQU87QUFDdkI7QUFXTyxTQUFTRSxjQUFjLEVBQzVCQyxLQUFLLEVBQ0xDLFVBQVUsRUFDVkMsZUFBZSxFQUNmQyxVQUFVLEVBQ1ZDLFdBQVcsQ0FBQyxFQUNPO0lBQ25CLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHeEQsK0NBQVFBLENBQUM7SUFFM0MsTUFBTXlELGVBQWVDLEtBQUtDLEtBQUssQ0FBQ0osWUFBWUo7SUFDNUMsTUFBTVMsYUFBYUYsS0FBS0csR0FBRyxDQUN6QkosZUFBZUMsS0FBS0ksSUFBSSxDQUFDVixrQkFBa0JELGFBQzNDRCxNQUFNYSxNQUFNLEdBQUc7SUFHakIsTUFBTUMsYUFBYVAsZUFBZU47SUFDbEMsTUFBTWMsZ0JBQWdCLENBQUNmLE1BQU1hLE1BQU0sR0FBR0gsYUFBYSxLQUFLVDtJQUV4RCxNQUFNZSxlQUFlL0QsOENBQU9BLENBQUM7UUFDM0IsT0FBTytDLE1BQU1pQixLQUFLLENBQ2hCVCxLQUFLVSxHQUFHLENBQUMsR0FBR1gsZUFBZUgsV0FDM0JJLEtBQUtHLEdBQUcsQ0FBQ1gsTUFBTWEsTUFBTSxFQUFFSCxhQUFhLElBQUlOO0lBRTVDLEdBQUc7UUFBQ0o7UUFBT087UUFBY0c7UUFBWU47S0FBUztJQUU5QyxxQkFDRSw4REFBQ2hDO1FBQ0MrQyxPQUFPO1lBQUV4QyxRQUFRdUI7WUFBaUJrQixVQUFVO1FBQU87UUFDbkRDLFVBQVUsQ0FBQ0MsSUFBTWhCLGFBQWFnQixFQUFFQyxhQUFhLENBQUNsQixTQUFTO2tCQUV2RCw0RUFBQ2pDO1lBQUkrQyxPQUFPO2dCQUFFTDtnQkFBWUM7WUFBYztzQkFDckNDLGFBQWFRLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDdkIsOERBQUN0RDtvQkFBMEMrQyxPQUFPO3dCQUFFeEMsUUFBUXNCO29CQUFXOzhCQUNwRUUsV0FBV3NCLE1BQU1sQixlQUFlSCxXQUFXc0I7bUJBRHBDbkIsZUFBZUgsV0FBV3NCOzs7Ozs7Ozs7Ozs7Ozs7QUFPOUM7QUFFQSx3QkFBd0I7QUFDakIsU0FBU0MsWUFBZUMsS0FBUSxFQUFFQyxLQUFhO0lBQ3BELE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBR2pGLCtDQUFRQSxDQUFJOEU7SUFFeEQ3RSxnREFBU0EsQ0FBQztRQUNSLE1BQU1pRixVQUFVQyxXQUFXO1lBQ3pCRixrQkFBa0JIO1FBQ3BCLEdBQUdDO1FBRUgsT0FBTztZQUNMSyxhQUFhRjtRQUNmO0lBQ0YsR0FBRztRQUFDSjtRQUFPQztLQUFNO0lBRWpCLE9BQU9DO0FBQ1Q7QUFVTyxTQUFTSyxzQkFBc0IsRUFDcENDLEtBQUssRUFDTHBDLEtBQUssRUFDTHFDLFlBQVksRUFDWmxDLFVBQVUsRUFDUztJQUNuQixNQUFNbUMsaUJBQWlCWCxZQUFZUyxPQUFPO0lBRTFDLE1BQU1HLGdCQUFnQnRGLDhDQUFPQSxDQUFDO1FBQzVCLElBQUksQ0FBQ3FGLGVBQWVFLElBQUksSUFBSSxPQUFPeEM7UUFFbkMsT0FBT0EsTUFBTXlDLE1BQU0sQ0FBQ2hCLENBQUFBLE9BQ2xCWSxhQUFhSyxJQUFJLENBQUNDLENBQUFBLFFBQ2hCbEIsSUFBSSxDQUFDa0IsTUFBTSxFQUFFQyxjQUFjQyxTQUFTUCxlQUFlTSxXQUFXO0lBR3BFLEdBQUc7UUFBQzVDO1FBQU9zQztRQUFnQkQ7S0FBYTtJQUV4QyxxQkFDRSw4REFBQ2pFO2tCQUNFbUUsY0FBY2YsR0FBRyxDQUFDLENBQUNDLE1BQU1DLHNCQUN4Qiw4REFBQ3REOzBCQUNFK0IsV0FBV3NCO2VBREpBLEtBQUtxQixFQUFFLElBQUlwQjs7Ozs7Ozs7OztBQU03QjtBQVVPLFNBQVNxQixnQkFBZ0IsRUFDOUJDLFFBQVEsRUFDUkMseUJBQVcsOERBQUM3RTtJQUFJQyxXQUFVOzs7OztRQUE4QyxFQUN4RWIsWUFBWSxHQUFHLEVBQ2ZhLFlBQVksRUFBRSxFQUNPO0lBQ3JCLE1BQU0sRUFBRVYsR0FBRyxFQUFFa0MsTUFBTSxFQUFFLEdBQUdELFlBQVlwQztJQUVwQyxxQkFDRSw4REFBQ1k7UUFBSVQsS0FBS0E7UUFBS1UsV0FBV0E7a0JBQ3ZCd0IsU0FBU21ELFdBQVdDOzs7Ozs7QUFHM0I7QUFFQSw4QkFBOEI7QUFDdkIsU0FBU0M7SUFDZCxNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR3RHLCtDQUFRQSxDQUFDO1FBQ3JDdUcsVUFBVTtRQUNWQyxZQUFZO1FBQ1pDLGFBQWE7SUFDZjtJQUVBeEcsZ0RBQVNBLENBQUM7UUFDUix5QkFBeUI7UUFDekIsTUFBTXNHLFdBQVdHLFlBQVlDLE1BQU0sQ0FBQ0MsWUFBWSxHQUFHRixZQUFZQyxNQUFNLENBQUNFLGVBQWU7UUFFckYsc0JBQXNCO1FBQ3RCLE1BQU1DLGNBQWNKLFlBQVlLLEdBQUc7UUFFbkMsOEJBQThCO1FBQzlCLE1BQU1OLGNBQWMsWUFBcUJPLE1BQU0sRUFBRUMsa0JBQWtCO1FBRW5FOUIsV0FBVztZQUNULE1BQU1xQixhQUFhRSxZQUFZSyxHQUFHLEtBQUtEO1lBQ3ZDUixXQUFXO2dCQUNUQztnQkFDQUM7Z0JBQ0FDO1lBQ0Y7UUFDRixHQUFHO1FBRUgseUJBQXlCO1FBQ3pCLElBQUk7WUFDRiw2TEFBTyxDQUFjUyxJQUFJLENBQUMsQ0FBQyxFQUFFQyxNQUFNLEVBQUVDLE1BQU0sRUFBRUMsTUFBTSxFQUFFQyxNQUFNLEVBQUVDLE9BQU8sRUFBRTtnQkFDcEVKLE9BQU9LLFFBQVFDLEdBQUc7Z0JBQ2xCTCxPQUFPSSxRQUFRQyxHQUFHO2dCQUNsQkosT0FBT0csUUFBUUMsR0FBRztnQkFDbEJILE9BQU9FLFFBQVFDLEdBQUc7Z0JBQ2xCRixRQUFRQyxRQUFRQyxHQUFHO1lBQ3JCLEdBQUdDLEtBQUssQ0FBQztZQUNQLGlDQUFpQztZQUNuQztRQUNGLEVBQUUsT0FBT3ZGLE9BQU87UUFDZCxpQ0FBaUM7UUFDbkM7SUFDRixHQUFHLEVBQUU7SUFFTCxPQUFPa0U7QUFDVDtBQVdPLFNBQVNzQixtQkFBbUIsRUFDakN6QixRQUFRLEVBQ1IwQixZQUFZLFFBQVEsRUFDcEJDLFdBQVcsR0FBRyxFQUNkOUMsUUFBUSxDQUFDLEVBQ1RyRSxZQUFZLEdBQUcsRUFDUztJQUN4QixNQUFNLEVBQUVHLEdBQUcsRUFBRWtDLE1BQU0sRUFBRSxHQUFHRCxZQUFZcEM7SUFFcEMsTUFBTW9ILGFBQWE7UUFDakJDLFFBQVE7WUFDTkMsU0FBUztnQkFBRUMsU0FBUztZQUFFO1lBQ3RCQyxTQUFTO2dCQUFFRCxTQUFTO1lBQUU7UUFDeEI7UUFDQUUsU0FBUztZQUNQSCxTQUFTO2dCQUFFQyxTQUFTO2dCQUFHRyxHQUFHO1lBQUc7WUFDN0JGLFNBQVM7Z0JBQUVELFNBQVM7Z0JBQUdHLEdBQUc7WUFBRTtRQUM5QjtRQUNBQyxXQUFXO1lBQ1RMLFNBQVM7Z0JBQUVDLFNBQVM7Z0JBQUdLLEdBQUc7WUFBRztZQUM3QkosU0FBUztnQkFBRUQsU0FBUztnQkFBR0ssR0FBRztZQUFFO1FBQzlCO1FBQ0FDLE9BQU87WUFDTFAsU0FBUztnQkFBRUMsU0FBUztnQkFBR00sT0FBTztZQUFJO1lBQ2xDTCxTQUFTO2dCQUFFRCxTQUFTO2dCQUFHTSxPQUFPO1lBQUU7UUFDbEM7SUFDRjtJQUVBLHFCQUNFLDhEQUFDakg7UUFBSVQsS0FBS0E7a0JBQ1IsNEVBQUNQLDBEQUFlQTtzQkFDYnlDLHdCQUNDLDhEQUFDMUMsaURBQU1BLENBQUNpQixHQUFHO2dCQUNUMEcsU0FBU0YsVUFBVSxDQUFDRixVQUFVLENBQUNJLE9BQU87Z0JBQ3RDRSxTQUFTSixVQUFVLENBQUNGLFVBQVUsQ0FBQ00sT0FBTztnQkFDdENNLFlBQVk7b0JBQUVYO29CQUFVOUM7Z0JBQU07MEJBRTdCbUI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNYjtBQUVBLHVCQUF1QjtBQUNoQixTQUFTdUMsa0JBQWtCQyxTQUFtQjtJQUNuRCxNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHNUksK0NBQVFBLENBQWMsSUFBSTZJO0lBRWxFNUksZ0RBQVNBLENBQUM7UUFDUixNQUFNNkksZ0JBQWdCO1lBQ3BCLE1BQU1DLFdBQVdMLFVBQVVoRSxHQUFHLENBQUNzRSxDQUFBQTtnQkFDN0IsT0FBTyxJQUFJQyxRQUFnQixDQUFDQyxTQUFTQztvQkFDbkMsTUFBTUMsTUFBTSxJQUFJQyxPQUFPOUksS0FBSztvQkFDNUI2SSxJQUFJRSxNQUFNLEdBQUcsSUFBTUosUUFBUUY7b0JBQzNCSSxJQUFJRyxPQUFPLEdBQUdKO29CQUNkQyxJQUFJMUgsR0FBRyxHQUFHc0g7Z0JBQ1o7WUFDRjtZQUVBLElBQUk7Z0JBQ0YsTUFBTVEsU0FBUyxNQUFNUCxRQUFRUSxVQUFVLENBQUNWO2dCQUN4QyxNQUFNVyxhQUFhRixPQUNoQjdELE1BQU0sQ0FBQ2dFLENBQUFBLFNBQVVBLE9BQU9DLE1BQU0sS0FBSyxhQUNuQ2xGLEdBQUcsQ0FBQ2lGLENBQUFBLFNBQVUsT0FBMkM3RSxLQUFLO2dCQUVqRThELGdCQUFnQixJQUFJQyxJQUFJYTtZQUMxQixFQUFFLE9BQU92SCxPQUFPO2dCQUNkcUYsUUFBUXJGLEtBQUssQ0FBQyw0QkFBNEJBO1lBQzVDO1FBQ0Y7UUFFQSxJQUFJdUcsVUFBVTNFLE1BQU0sR0FBRyxHQUFHO1lBQ3hCK0U7UUFDRjtJQUNGLEdBQUc7UUFBQ0o7S0FBVTtJQUVkLE9BQU9DO0FBQ1Q7QUFFQSwwQ0FBMEM7QUFDbkMsU0FBU2tCO0lBQ2QsSUFBSUMsS0FBeUIsRUFBZSxFQUUzQztJQUVELE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHaEssK0NBQVFBLENBQU07SUFFbERDLGdEQUFTQSxDQUFDO1FBQ1IsbUVBQW1FO1FBQ25FLElBQUksS0FBb0UsRUFBRSxFQUV6RTtJQUNILEdBQUcsRUFBRTtJQUVMLElBQUksQ0FBQzhKLFlBQVksT0FBTztJQUV4QixxQkFDRSw4REFBQ3pJO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDMkk7Z0JBQUczSSxXQUFVOzBCQUFpQjs7Ozs7OzBCQUMvQiw4REFBQ0Q7O29CQUFJO29CQUFheUksV0FBV0ksU0FBUzs7Ozs7OzswQkFDdEMsOERBQUM3STs7b0JBQUk7b0JBQVN5SSxXQUFXSyxNQUFNOzs7Ozs7OzBCQUMvQiw4REFBQzlJOztvQkFBSTtvQkFBVXlJLFdBQVdNLE9BQU87Ozs7Ozs7Ozs7Ozs7QUFHdkM7QUFFQSwrQkFBK0I7QUFDeEIsU0FBU0M7SUFDZCxNQUFNakUsVUFBVUQ7SUFDaEIsTUFBTSxDQUFDbUUsVUFBVUMsWUFBWSxHQUFHeEssK0NBQVFBLENBQVcsRUFBRTtJQUVyREMsZ0RBQVNBLENBQUM7UUFDUixNQUFNd0ssY0FBd0IsRUFBRTtRQUVoQyxJQUFJcEUsUUFBUUUsUUFBUSxHQUFHLE1BQU07WUFDM0JrRSxZQUFZQyxJQUFJLENBQUM7UUFDbkI7UUFFQSxJQUFJckUsUUFBUUksV0FBVyxHQUFHLEtBQUssT0FBTyxNQUFNO1lBQzFDZ0UsWUFBWUMsSUFBSSxDQUFDO1FBQ25CO1FBRUFGLFlBQVlDO0lBQ2QsR0FBRztRQUFDcEU7S0FBUTtJQUVaLElBQUl5RCxNQUF5QixJQUFpQlMsU0FBU3hHLE1BQU0sS0FBSyxHQUFHO1FBQ25FLE9BQU87SUFDVDtJQUVBLHFCQUNFLDhEQUFDekM7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUMySTtnQkFBRzNJLFdBQVU7MEJBQWlCOzs7Ozs7MEJBQy9CLDhEQUFDb0o7Z0JBQUdwSixXQUFVOzBCQUNYZ0osU0FBUzdGLEdBQUcsQ0FBQyxDQUFDa0csU0FBU2hHLHNCQUN0Qiw4REFBQ2lHOzs0QkFBZTs0QkFBR0Q7O3VCQUFWaEc7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wb3NpdGl2ZTctdG91cmlzbS13ZWJzaXRlLy4vY29tcG9uZW50cy9wZXJmb3JtYW5jZS9QZXJmb3JtYW5jZU9wdGltaXphdGlvbi50c3g/M2FlZiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2ssIHVzZU1lbW8sIHVzZVJlZiB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tICdmcmFtZXItbW90aW9uJ1xuaW1wb3J0IEltYWdlIGZyb20gJ25leHQvaW1hZ2UnXG5pbXBvcnQgZHluYW1pYyBmcm9tICduZXh0L2R5bmFtaWMnXG5cbi8vIFNpbXBsZSBpbnRlcnNlY3Rpb24gb2JzZXJ2ZXIgaG9vayBpbXBsZW1lbnRhdGlvblxuZnVuY3Rpb24gdXNlSW50ZXJzZWN0aW9uT2JzZXJ2ZXIodGhyZXNob2xkID0gMC4xKSB7XG4gIGNvbnN0IFtpc0ludGVyc2VjdGluZywgc2V0SXNJbnRlcnNlY3RpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IHJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IG9ic2VydmVyID0gbmV3IEludGVyc2VjdGlvbk9ic2VydmVyKFxuICAgICAgKFtlbnRyeV0pID0+IHtcbiAgICAgICAgc2V0SXNJbnRlcnNlY3RpbmcoZW50cnkuaXNJbnRlcnNlY3RpbmcpXG4gICAgICB9LFxuICAgICAgeyB0aHJlc2hvbGQgfVxuICAgIClcblxuICAgIGlmIChyZWYuY3VycmVudCkge1xuICAgICAgb2JzZXJ2ZXIub2JzZXJ2ZShyZWYuY3VycmVudClcbiAgICB9XG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgaWYgKHJlZi5jdXJyZW50KSB7XG4gICAgICAgIG9ic2VydmVyLnVub2JzZXJ2ZShyZWYuY3VycmVudClcbiAgICAgIH1cbiAgICB9XG4gIH0sIFt0aHJlc2hvbGRdKVxuXG4gIHJldHVybiB7IHJlZiwgaXNJbnRlcnNlY3RpbmcgfVxufVxuXG4vLyBMYXp5IGxvYWQgaGVhdnkgY29tcG9uZW50c1xuY29uc3QgTGF6eUNoYXJ0ID0gZHluYW1pYygoKSA9PiBpbXBvcnQoJ3JlY2hhcnRzJykudGhlbihtb2QgPT4gbW9kLkJhckNoYXJ0KSwge1xuICBsb2FkaW5nOiAoKSA9PiA8ZGl2IGNsYXNzTmFtZT1cImgtNjQgYmctZ3JheS0yMDAgYW5pbWF0ZS1wdWxzZSByb3VuZGVkLWxnXCIgLz4sXG4gIHNzcjogZmFsc2Vcbn0pXG5cbi8vIE9wdGltaXplZCBJbWFnZSBDb21wb25lbnQgd2l0aCBsYXp5IGxvYWRpbmcgYW5kIGJsdXIgcGxhY2Vob2xkZXJcbmludGVyZmFjZSBPcHRpbWl6ZWRJbWFnZVByb3BzIHtcbiAgc3JjOiBzdHJpbmdcbiAgYWx0OiBzdHJpbmdcbiAgd2lkdGg/OiBudW1iZXJcbiAgaGVpZ2h0PzogbnVtYmVyXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xuICBwcmlvcml0eT86IGJvb2xlYW5cbiAgc2l6ZXM/OiBzdHJpbmdcbiAgZmlsbD86IGJvb2xlYW5cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIE9wdGltaXplZEltYWdlKHtcbiAgc3JjLFxuICBhbHQsXG4gIHdpZHRoLFxuICBoZWlnaHQsXG4gIGNsYXNzTmFtZSA9ICcnLFxuICBwcmlvcml0eSA9IGZhbHNlLFxuICBzaXplcyA9ICcobWF4LXdpZHRoOiA3NjhweCkgMTAwdncsIChtYXgtd2lkdGg6IDEyMDBweCkgNTB2dywgMzN2dycsXG4gIGZpbGwgPSBmYWxzZVxufTogT3B0aW1pemVkSW1hZ2VQcm9wcykge1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZShmYWxzZSlcblxuICBjb25zdCBoYW5kbGVMb2FkID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHNldElzTG9hZGluZyhmYWxzZSlcbiAgfSwgW10pXG5cbiAgY29uc3QgaGFuZGxlRXJyb3IgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgc2V0RXJyb3IodHJ1ZSlcbiAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gIH0sIFtdKVxuXG4gIGlmIChlcnJvcikge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17YGJnLWdyYXktMjAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyICR7Y2xhc3NOYW1lfWB9PlxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtc21cIj5JbWFnZSB1bmF2YWlsYWJsZTwvc3Bhbj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2ByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW4gJHtjbGFzc05hbWV9YH0+XG4gICAgICA8SW1hZ2VcbiAgICAgICAgc3JjPXtzcmN9XG4gICAgICAgIGFsdD17YWx0fVxuICAgICAgICB3aWR0aD17ZmlsbCA/IHVuZGVmaW5lZCA6IHdpZHRofVxuICAgICAgICBoZWlnaHQ9e2ZpbGwgPyB1bmRlZmluZWQgOiBoZWlnaHR9XG4gICAgICAgIGZpbGw9e2ZpbGx9XG4gICAgICAgIHNpemVzPXtzaXplc31cbiAgICAgICAgcHJpb3JpdHk9e3ByaW9yaXR5fVxuICAgICAgICBxdWFsaXR5PXs4NX1cbiAgICAgICAgcGxhY2Vob2xkZXI9XCJibHVyXCJcbiAgICAgICAgYmx1ckRhdGFVUkw9XCJkYXRhOmltYWdlL2pwZWc7YmFzZTY0LC85ai80QUFRU2taSlJnQUJBUUFBQVFBQkFBRC8yd0JEQUFZRUJRWUZCQVlHQlFZSEJ3WUlDaEFLQ2drSkNoUU9Ed3dRRnhRWUdCY1VGaFlhSFNVZkdoc2pIQllXSUN3Z0l5WW5LU29wR1I4dE1DMG9NQ1VvS1NqLzJ3QkRBUWNIQndvSUNoTUtDaE1vR2hZYUtDZ29LQ2dvS0Nnb0tDZ29LQ2dvS0Nnb0tDZ29LQ2dvS0Nnb0tDZ29LQ2dvS0Nnb0tDZ29LQ2dvS0Nnb0tDZ29LQ2ovd0FBUkNBQUlBQW9EQVNJQUFoRUJBeEVCLzhRQUZRQUJBUUFBQUFBQUFBQUFBQUFBQUFBQUFBdi94QUFoRUFBQ0FRTURCUUFBQUFBQUFBQUFBQUFCQWdNQUJBVUdJV0dSa3FHeDBmL0VBQlVCQVFFQUFBQUFBQUFBQUFBQUFBQUFBQU1GLzhRQUdoRUFBZ0lEQUFBQUFBQUFBQUFBQUFBQUFBRUNFZ01Sa2YvYUFBd0RBUUFDRVFNUkFEOEFsdEphZ3llSDBBdGhJNXhkckxjTk05MUJGNXBYMkhhSDliY2ZhU1hXR2FSbWtueUpja2xpeWpxVHpTbFQ1NGI2YmsraDBSLy8yUT09XCJcbiAgICAgICAgb25Mb2FkPXtoYW5kbGVMb2FkfVxuICAgICAgICBvbkVycm9yPXtoYW5kbGVFcnJvcn1cbiAgICAgICAgY2xhc3NOYW1lPXtgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgIGlzTG9hZGluZyA/ICdvcGFjaXR5LTAnIDogJ29wYWNpdHktMTAwJ1xuICAgICAgICB9ICR7ZmlsbCA/ICdvYmplY3QtY292ZXInIDogJyd9YH1cbiAgICAgIC8+XG4gICAgICB7aXNMb2FkaW5nICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYXktMjAwIGFuaW1hdGUtcHVsc2VcIiAvPlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG4vLyBJbnRlcnNlY3Rpb24gT2JzZXJ2ZXIgSG9vayBmb3IgbGF6eSBsb2FkaW5nXG5leHBvcnQgZnVuY3Rpb24gdXNlTGF6eUxvYWQodGhyZXNob2xkID0gMC4xKSB7XG4gIGNvbnN0IHsgcmVmLCBpc0ludGVyc2VjdGluZyB9ID0gdXNlSW50ZXJzZWN0aW9uT2JzZXJ2ZXIodGhyZXNob2xkKVxuICBjb25zdCBbaW5WaWV3LCBzZXRJblZpZXddID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaXNJbnRlcnNlY3RpbmcgJiYgIWluVmlldykge1xuICAgICAgc2V0SW5WaWV3KHRydWUpIC8vIE9uY2UgaW4gdmlldywgc3RheSBpbiB2aWV3ICh0cmlnZ2VyT25jZSBiZWhhdmlvcilcbiAgICB9XG4gIH0sIFtpc0ludGVyc2VjdGluZywgaW5WaWV3XSlcblxuICByZXR1cm4geyByZWYsIGluVmlldyB9XG59XG5cbi8vIFZpcnR1YWwgU2Nyb2xsaW5nIENvbXBvbmVudCBmb3IgbGFyZ2UgbGlzdHNcbmludGVyZmFjZSBWaXJ0dWFsU2Nyb2xsUHJvcHMge1xuICBpdGVtczogYW55W11cbiAgaXRlbUhlaWdodDogbnVtYmVyXG4gIGNvbnRhaW5lckhlaWdodDogbnVtYmVyXG4gIHJlbmRlckl0ZW06IChpdGVtOiBhbnksIGluZGV4OiBudW1iZXIpID0+IFJlYWN0LlJlYWN0Tm9kZVxuICBvdmVyc2Nhbj86IG51bWJlclxufVxuXG5leHBvcnQgZnVuY3Rpb24gVmlydHVhbFNjcm9sbCh7XG4gIGl0ZW1zLFxuICBpdGVtSGVpZ2h0LFxuICBjb250YWluZXJIZWlnaHQsXG4gIHJlbmRlckl0ZW0sXG4gIG92ZXJzY2FuID0gNVxufTogVmlydHVhbFNjcm9sbFByb3BzKSB7XG4gIGNvbnN0IFtzY3JvbGxUb3AsIHNldFNjcm9sbFRvcF0gPSB1c2VTdGF0ZSgwKVxuXG4gIGNvbnN0IHZpc2libGVTdGFydCA9IE1hdGguZmxvb3Ioc2Nyb2xsVG9wIC8gaXRlbUhlaWdodClcbiAgY29uc3QgdmlzaWJsZUVuZCA9IE1hdGgubWluKFxuICAgIHZpc2libGVTdGFydCArIE1hdGguY2VpbChjb250YWluZXJIZWlnaHQgLyBpdGVtSGVpZ2h0KSxcbiAgICBpdGVtcy5sZW5ndGggLSAxXG4gIClcblxuICBjb25zdCBwYWRkaW5nVG9wID0gdmlzaWJsZVN0YXJ0ICogaXRlbUhlaWdodFxuICBjb25zdCBwYWRkaW5nQm90dG9tID0gKGl0ZW1zLmxlbmd0aCAtIHZpc2libGVFbmQgLSAxKSAqIGl0ZW1IZWlnaHRcblxuICBjb25zdCB2aXNpYmxlSXRlbXMgPSB1c2VNZW1vKCgpID0+IHtcbiAgICByZXR1cm4gaXRlbXMuc2xpY2UoXG4gICAgICBNYXRoLm1heCgwLCB2aXNpYmxlU3RhcnQgLSBvdmVyc2NhbiksXG4gICAgICBNYXRoLm1pbihpdGVtcy5sZW5ndGgsIHZpc2libGVFbmQgKyAxICsgb3ZlcnNjYW4pXG4gICAgKVxuICB9LCBbaXRlbXMsIHZpc2libGVTdGFydCwgdmlzaWJsZUVuZCwgb3ZlcnNjYW5dKVxuXG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgc3R5bGU9e3sgaGVpZ2h0OiBjb250YWluZXJIZWlnaHQsIG92ZXJmbG93OiAnYXV0bycgfX1cbiAgICAgIG9uU2Nyb2xsPXsoZSkgPT4gc2V0U2Nyb2xsVG9wKGUuY3VycmVudFRhcmdldC5zY3JvbGxUb3ApfVxuICAgID5cbiAgICAgIDxkaXYgc3R5bGU9e3sgcGFkZGluZ1RvcCwgcGFkZGluZ0JvdHRvbSB9fT5cbiAgICAgICAge3Zpc2libGVJdGVtcy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXG4gICAgICAgICAgPGRpdiBrZXk9e3Zpc2libGVTdGFydCAtIG92ZXJzY2FuICsgaW5kZXh9IHN0eWxlPXt7IGhlaWdodDogaXRlbUhlaWdodCB9fT5cbiAgICAgICAgICAgIHtyZW5kZXJJdGVtKGl0ZW0sIHZpc2libGVTdGFydCAtIG92ZXJzY2FuICsgaW5kZXgpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG5cbi8vIERlYm91bmNlZCBTZWFyY2ggSG9va1xuZXhwb3J0IGZ1bmN0aW9uIHVzZURlYm91bmNlPFQ+KHZhbHVlOiBULCBkZWxheTogbnVtYmVyKTogVCB7XG4gIGNvbnN0IFtkZWJvdW5jZWRWYWx1ZSwgc2V0RGVib3VuY2VkVmFsdWVdID0gdXNlU3RhdGU8VD4odmFsdWUpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVyID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBzZXREZWJvdW5jZWRWYWx1ZSh2YWx1ZSlcbiAgICB9LCBkZWxheSlcblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBjbGVhclRpbWVvdXQoaGFuZGxlcilcbiAgICB9XG4gIH0sIFt2YWx1ZSwgZGVsYXldKVxuXG4gIHJldHVybiBkZWJvdW5jZWRWYWx1ZVxufVxuXG4vLyBNZW1vaXplZCBTZWFyY2ggQ29tcG9uZW50XG5pbnRlcmZhY2UgU2VhcmNoUmVzdWx0c1Byb3BzIHtcbiAgcXVlcnk6IHN0cmluZ1xuICBpdGVtczogYW55W11cbiAgc2VhcmNoRmllbGRzOiBzdHJpbmdbXVxuICByZW5kZXJJdGVtOiAoaXRlbTogYW55KSA9PiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIE1lbW9pemVkU2VhcmNoUmVzdWx0cyh7XG4gIHF1ZXJ5LFxuICBpdGVtcyxcbiAgc2VhcmNoRmllbGRzLFxuICByZW5kZXJJdGVtXG59OiBTZWFyY2hSZXN1bHRzUHJvcHMpIHtcbiAgY29uc3QgZGVib3VuY2VkUXVlcnkgPSB1c2VEZWJvdW5jZShxdWVyeSwgMzAwKVxuXG4gIGNvbnN0IGZpbHRlcmVkSXRlbXMgPSB1c2VNZW1vKCgpID0+IHtcbiAgICBpZiAoIWRlYm91bmNlZFF1ZXJ5LnRyaW0oKSkgcmV0dXJuIGl0ZW1zXG5cbiAgICByZXR1cm4gaXRlbXMuZmlsdGVyKGl0ZW0gPT5cbiAgICAgIHNlYXJjaEZpZWxkcy5zb21lKGZpZWxkID0+XG4gICAgICAgIGl0ZW1bZmllbGRdPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGRlYm91bmNlZFF1ZXJ5LnRvTG93ZXJDYXNlKCkpXG4gICAgICApXG4gICAgKVxuICB9LCBbaXRlbXMsIGRlYm91bmNlZFF1ZXJ5LCBzZWFyY2hGaWVsZHNdKVxuXG4gIHJldHVybiAoXG4gICAgPGRpdj5cbiAgICAgIHtmaWx0ZXJlZEl0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgPGRpdiBrZXk9e2l0ZW0uaWQgfHwgaW5kZXh9PlxuICAgICAgICAgIHtyZW5kZXJJdGVtKGl0ZW0pfVxuICAgICAgICA8L2Rpdj5cbiAgICAgICkpfVxuICAgIDwvZGl2PlxuICApXG59XG5cbi8vIExhenkgTG9hZGluZyBTZWN0aW9uIENvbXBvbmVudFxuaW50ZXJmYWNlIExhenlMb2FkU2VjdGlvblByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxuICBmYWxsYmFjaz86IFJlYWN0LlJlYWN0Tm9kZVxuICB0aHJlc2hvbGQ/OiBudW1iZXJcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBMYXp5TG9hZFNlY3Rpb24oe1xuICBjaGlsZHJlbixcbiAgZmFsbGJhY2sgPSA8ZGl2IGNsYXNzTmFtZT1cImgtNjQgYmctZ3JheS0yMDAgYW5pbWF0ZS1wdWxzZSByb3VuZGVkLWxnXCIgLz4sXG4gIHRocmVzaG9sZCA9IDAuMSxcbiAgY2xhc3NOYW1lID0gJydcbn06IExhenlMb2FkU2VjdGlvblByb3BzKSB7XG4gIGNvbnN0IHsgcmVmLCBpblZpZXcgfSA9IHVzZUxhenlMb2FkKHRocmVzaG9sZClcblxuICByZXR1cm4gKFxuICAgIDxkaXYgcmVmPXtyZWZ9IGNsYXNzTmFtZT17Y2xhc3NOYW1lfT5cbiAgICAgIHtpblZpZXcgPyBjaGlsZHJlbiA6IGZhbGxiYWNrfVxuICAgIDwvZGl2PlxuICApXG59XG5cbi8vIFBlcmZvcm1hbmNlIE1vbml0b3JpbmcgSG9va1xuZXhwb3J0IGZ1bmN0aW9uIHVzZVBlcmZvcm1hbmNlTW9uaXRvcmluZygpIHtcbiAgY29uc3QgW21ldHJpY3MsIHNldE1ldHJpY3NdID0gdXNlU3RhdGUoe1xuICAgIGxvYWRUaW1lOiAwLFxuICAgIHJlbmRlclRpbWU6IDAsXG4gICAgbWVtb3J5VXNhZ2U6IDBcbiAgfSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIE1lYXN1cmUgcGFnZSBsb2FkIHRpbWVcbiAgICBjb25zdCBsb2FkVGltZSA9IHBlcmZvcm1hbmNlLnRpbWluZy5sb2FkRXZlbnRFbmQgLSBwZXJmb3JtYW5jZS50aW1pbmcubmF2aWdhdGlvblN0YXJ0XG5cbiAgICAvLyBNZWFzdXJlIHJlbmRlciB0aW1lXG4gICAgY29uc3QgcmVuZGVyU3RhcnQgPSBwZXJmb3JtYW5jZS5ub3coKVxuXG4gICAgLy8gTWVtb3J5IHVzYWdlIChpZiBhdmFpbGFibGUpXG4gICAgY29uc3QgbWVtb3J5VXNhZ2UgPSAocGVyZm9ybWFuY2UgYXMgYW55KS5tZW1vcnk/LnVzZWRKU0hlYXBTaXplIHx8IDBcblxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgY29uc3QgcmVuZGVyVGltZSA9IHBlcmZvcm1hbmNlLm5vdygpIC0gcmVuZGVyU3RhcnRcbiAgICAgIHNldE1ldHJpY3Moe1xuICAgICAgICBsb2FkVGltZSxcbiAgICAgICAgcmVuZGVyVGltZSxcbiAgICAgICAgbWVtb3J5VXNhZ2VcbiAgICAgIH0pXG4gICAgfSwgMClcblxuICAgIC8vIFJlcG9ydCBDb3JlIFdlYiBWaXRhbHNcbiAgICB0cnkge1xuICAgICAgaW1wb3J0KCd3ZWItdml0YWxzJykudGhlbigoeyBnZXRDTFMsIGdldEZJRCwgZ2V0RkNQLCBnZXRMQ1AsIGdldFRURkIgfSkgPT4ge1xuICAgICAgICBnZXRDTFMoY29uc29sZS5sb2cpXG4gICAgICAgIGdldEZJRChjb25zb2xlLmxvZylcbiAgICAgICAgZ2V0RkNQKGNvbnNvbGUubG9nKVxuICAgICAgICBnZXRMQ1AoY29uc29sZS5sb2cpXG4gICAgICAgIGdldFRURkIoY29uc29sZS5sb2cpXG4gICAgICB9KS5jYXRjaCgoKSA9PiB7XG4gICAgICAgIC8vIHdlYi12aXRhbHMgbm90IGF2YWlsYWJsZSwgc2tpcFxuICAgICAgfSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgLy8gd2ViLXZpdGFscyBub3QgYXZhaWxhYmxlLCBza2lwXG4gICAgfVxuICB9LCBbXSlcblxuICByZXR1cm4gbWV0cmljc1xufVxuXG4vLyBPcHRpbWl6ZWQgQW5pbWF0aW9uIENvbXBvbmVudFxuaW50ZXJmYWNlIE9wdGltaXplZEFuaW1hdGlvblByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxuICBhbmltYXRpb24/OiAnZmFkZUluJyB8ICdzbGlkZVVwJyB8ICdzbGlkZUxlZnQnIHwgJ3NjYWxlJ1xuICBkdXJhdGlvbj86IG51bWJlclxuICBkZWxheT86IG51bWJlclxuICB0aHJlc2hvbGQ/OiBudW1iZXJcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIE9wdGltaXplZEFuaW1hdGlvbih7XG4gIGNoaWxkcmVuLFxuICBhbmltYXRpb24gPSAnZmFkZUluJyxcbiAgZHVyYXRpb24gPSAwLjUsXG4gIGRlbGF5ID0gMCxcbiAgdGhyZXNob2xkID0gMC4xXG59OiBPcHRpbWl6ZWRBbmltYXRpb25Qcm9wcykge1xuICBjb25zdCB7IHJlZiwgaW5WaWV3IH0gPSB1c2VMYXp5TG9hZCh0aHJlc2hvbGQpXG5cbiAgY29uc3QgYW5pbWF0aW9ucyA9IHtcbiAgICBmYWRlSW46IHtcbiAgICAgIGluaXRpYWw6IHsgb3BhY2l0eTogMCB9LFxuICAgICAgYW5pbWF0ZTogeyBvcGFjaXR5OiAxIH1cbiAgICB9LFxuICAgIHNsaWRlVXA6IHtcbiAgICAgIGluaXRpYWw6IHsgb3BhY2l0eTogMCwgeTogMjAgfSxcbiAgICAgIGFuaW1hdGU6IHsgb3BhY2l0eTogMSwgeTogMCB9XG4gICAgfSxcbiAgICBzbGlkZUxlZnQ6IHtcbiAgICAgIGluaXRpYWw6IHsgb3BhY2l0eTogMCwgeDogMjAgfSxcbiAgICAgIGFuaW1hdGU6IHsgb3BhY2l0eTogMSwgeDogMCB9XG4gICAgfSxcbiAgICBzY2FsZToge1xuICAgICAgaW5pdGlhbDogeyBvcGFjaXR5OiAwLCBzY2FsZTogMC45IH0sXG4gICAgICBhbmltYXRlOiB7IG9wYWNpdHk6IDEsIHNjYWxlOiAxIH1cbiAgICB9XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgcmVmPXtyZWZ9PlxuICAgICAgPEFuaW1hdGVQcmVzZW5jZT5cbiAgICAgICAge2luVmlldyAmJiAoXG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e2FuaW1hdGlvbnNbYW5pbWF0aW9uXS5pbml0aWFsfVxuICAgICAgICAgICAgYW5pbWF0ZT17YW5pbWF0aW9uc1thbmltYXRpb25dLmFuaW1hdGV9XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uLCBkZWxheSB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICl9XG4gICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG4vLyBJbWFnZSBQcmVsb2FkZXIgSG9va1xuZXhwb3J0IGZ1bmN0aW9uIHVzZUltYWdlUHJlbG9hZGVyKGltYWdlVXJsczogc3RyaW5nW10pIHtcbiAgY29uc3QgW2xvYWRlZEltYWdlcywgc2V0TG9hZGVkSW1hZ2VzXSA9IHVzZVN0YXRlPFNldDxzdHJpbmc+PihuZXcgU2V0KCkpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBwcmVsb2FkSW1hZ2VzID0gYXN5bmMgKCkgPT4ge1xuICAgICAgY29uc3QgcHJvbWlzZXMgPSBpbWFnZVVybHMubWFwKHVybCA9PiB7XG4gICAgICAgIHJldHVybiBuZXcgUHJvbWlzZTxzdHJpbmc+KChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgICAgICBjb25zdCBpbWcgPSBuZXcgd2luZG93LkltYWdlKClcbiAgICAgICAgICBpbWcub25sb2FkID0gKCkgPT4gcmVzb2x2ZSh1cmwpXG4gICAgICAgICAgaW1nLm9uZXJyb3IgPSByZWplY3RcbiAgICAgICAgICBpbWcuc3JjID0gdXJsXG4gICAgICAgIH0pXG4gICAgICB9KVxuXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBsb2FkZWQgPSBhd2FpdCBQcm9taXNlLmFsbFNldHRsZWQocHJvbWlzZXMpXG4gICAgICAgIGNvbnN0IHN1Y2Nlc3NmdWwgPSBsb2FkZWRcbiAgICAgICAgICAuZmlsdGVyKHJlc3VsdCA9PiByZXN1bHQuc3RhdHVzID09PSAnZnVsZmlsbGVkJylcbiAgICAgICAgICAubWFwKHJlc3VsdCA9PiAocmVzdWx0IGFzIFByb21pc2VGdWxmaWxsZWRSZXN1bHQ8c3RyaW5nPikudmFsdWUpXG5cbiAgICAgICAgc2V0TG9hZGVkSW1hZ2VzKG5ldyBTZXQoc3VjY2Vzc2Z1bCkpXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwcmVsb2FkaW5nIGltYWdlczonLCBlcnJvcilcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoaW1hZ2VVcmxzLmxlbmd0aCA+IDApIHtcbiAgICAgIHByZWxvYWRJbWFnZXMoKVxuICAgIH1cbiAgfSwgW2ltYWdlVXJsc10pXG5cbiAgcmV0dXJuIGxvYWRlZEltYWdlc1xufVxuXG4vLyBCdW5kbGUgU2l6ZSBBbmFseXplciAoRGV2ZWxvcG1lbnQgb25seSlcbmV4cG9ydCBmdW5jdGlvbiBCdW5kbGVBbmFseXplcigpIHtcbiAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAnZGV2ZWxvcG1lbnQnKSB7XG4gICAgcmV0dXJuIG51bGxcbiAgfVxuXG4gIGNvbnN0IFtidW5kbGVJbmZvLCBzZXRCdW5kbGVJbmZvXSA9IHVzZVN0YXRlPGFueT4obnVsbClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFRoaXMgd291bGQgaW50ZWdyYXRlIHdpdGggd2VicGFjay1idW5kbGUtYW5hbHl6ZXIgaW4gZGV2ZWxvcG1lbnRcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgKHdpbmRvdyBhcyBhbnkpLl9fQlVORExFX0FOQUxZWkVSX18pIHtcbiAgICAgIHNldEJ1bmRsZUluZm8oKHdpbmRvdyBhcyBhbnkpLl9fQlVORExFX0FOQUxZWkVSX18pXG4gICAgfVxuICB9LCBbXSlcblxuICBpZiAoIWJ1bmRsZUluZm8pIHJldHVybiBudWxsXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGJvdHRvbS00IHJpZ2h0LTQgYmctYmxhY2sgdGV4dC13aGl0ZSBwLTQgcm91bmRlZC1sZyB0ZXh0LXhzXCI+XG4gICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1ib2xkIG1iLTJcIj5CdW5kbGUgSW5mbzwvaDQ+XG4gICAgICA8ZGl2PlRvdGFsIFNpemU6IHtidW5kbGVJbmZvLnRvdGFsU2l6ZX08L2Rpdj5cbiAgICAgIDxkaXY+Q2h1bmtzOiB7YnVuZGxlSW5mby5jaHVua3N9PC9kaXY+XG4gICAgICA8ZGl2Pk1vZHVsZXM6IHtidW5kbGVJbmZvLm1vZHVsZXN9PC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cblxuLy8gUGVyZm9ybWFuY2UgQnVkZ2V0IENvbXBvbmVudFxuZXhwb3J0IGZ1bmN0aW9uIFBlcmZvcm1hbmNlQnVkZ2V0KCkge1xuICBjb25zdCBtZXRyaWNzID0gdXNlUGVyZm9ybWFuY2VNb25pdG9yaW5nKClcbiAgY29uc3QgW3dhcm5pbmdzLCBzZXRXYXJuaW5nc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oW10pXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBuZXdXYXJuaW5nczogc3RyaW5nW10gPSBbXVxuXG4gICAgaWYgKG1ldHJpY3MubG9hZFRpbWUgPiAzMDAwKSB7XG4gICAgICBuZXdXYXJuaW5ncy5wdXNoKCdQYWdlIGxvYWQgdGltZSBleGNlZWRzIDMgc2Vjb25kcycpXG4gICAgfVxuXG4gICAgaWYgKG1ldHJpY3MubWVtb3J5VXNhZ2UgPiA1MCAqIDEwMjQgKiAxMDI0KSB7IC8vIDUwTUJcbiAgICAgIG5ld1dhcm5pbmdzLnB1c2goJ01lbW9yeSB1c2FnZSBleGNlZWRzIDUwTUInKVxuICAgIH1cblxuICAgIHNldFdhcm5pbmdzKG5ld1dhcm5pbmdzKVxuICB9LCBbbWV0cmljc10pXG5cbiAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAnZGV2ZWxvcG1lbnQnIHx8IHdhcm5pbmdzLmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiBudWxsXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgdG9wLTQgcmlnaHQtNCBiZy1yZWQtNTAwIHRleHQtd2hpdGUgcC00IHJvdW5kZWQtbGcgdGV4dC1zbSBtYXgtdy14c1wiPlxuICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtYm9sZCBtYi0yXCI+UGVyZm9ybWFuY2UgV2FybmluZ3M8L2g0PlxuICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMVwiPlxuICAgICAgICB7d2FybmluZ3MubWFwKCh3YXJuaW5nLCBpbmRleCkgPT4gKFxuICAgICAgICAgIDxsaSBrZXk9e2luZGV4fT7igKIge3dhcm5pbmd9PC9saT5cbiAgICAgICAgKSl9XG4gICAgICA8L3VsPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsInVzZU1lbW8iLCJ1c2VSZWYiLCJtb3Rpb24iLCJBbmltYXRlUHJlc2VuY2UiLCJJbWFnZSIsImR5bmFtaWMiLCJ1c2VJbnRlcnNlY3Rpb25PYnNlcnZlciIsInRocmVzaG9sZCIsImlzSW50ZXJzZWN0aW5nIiwic2V0SXNJbnRlcnNlY3RpbmciLCJyZWYiLCJvYnNlcnZlciIsIkludGVyc2VjdGlvbk9ic2VydmVyIiwiZW50cnkiLCJjdXJyZW50Iiwib2JzZXJ2ZSIsInVub2JzZXJ2ZSIsIkxhenlDaGFydCIsImxvYWRpbmciLCJkaXYiLCJjbGFzc05hbWUiLCJzc3IiLCJPcHRpbWl6ZWRJbWFnZSIsInNyYyIsImFsdCIsIndpZHRoIiwiaGVpZ2h0IiwicHJpb3JpdHkiLCJzaXplcyIsImZpbGwiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwiaGFuZGxlTG9hZCIsImhhbmRsZUVycm9yIiwic3BhbiIsInVuZGVmaW5lZCIsInF1YWxpdHkiLCJwbGFjZWhvbGRlciIsImJsdXJEYXRhVVJMIiwib25Mb2FkIiwib25FcnJvciIsInVzZUxhenlMb2FkIiwiaW5WaWV3Iiwic2V0SW5WaWV3IiwiVmlydHVhbFNjcm9sbCIsIml0ZW1zIiwiaXRlbUhlaWdodCIsImNvbnRhaW5lckhlaWdodCIsInJlbmRlckl0ZW0iLCJvdmVyc2NhbiIsInNjcm9sbFRvcCIsInNldFNjcm9sbFRvcCIsInZpc2libGVTdGFydCIsIk1hdGgiLCJmbG9vciIsInZpc2libGVFbmQiLCJtaW4iLCJjZWlsIiwibGVuZ3RoIiwicGFkZGluZ1RvcCIsInBhZGRpbmdCb3R0b20iLCJ2aXNpYmxlSXRlbXMiLCJzbGljZSIsIm1heCIsInN0eWxlIiwib3ZlcmZsb3ciLCJvblNjcm9sbCIsImUiLCJjdXJyZW50VGFyZ2V0IiwibWFwIiwiaXRlbSIsImluZGV4IiwidXNlRGVib3VuY2UiLCJ2YWx1ZSIsImRlbGF5IiwiZGVib3VuY2VkVmFsdWUiLCJzZXREZWJvdW5jZWRWYWx1ZSIsImhhbmRsZXIiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwiTWVtb2l6ZWRTZWFyY2hSZXN1bHRzIiwicXVlcnkiLCJzZWFyY2hGaWVsZHMiLCJkZWJvdW5jZWRRdWVyeSIsImZpbHRlcmVkSXRlbXMiLCJ0cmltIiwiZmlsdGVyIiwic29tZSIsImZpZWxkIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsImlkIiwiTGF6eUxvYWRTZWN0aW9uIiwiY2hpbGRyZW4iLCJmYWxsYmFjayIsInVzZVBlcmZvcm1hbmNlTW9uaXRvcmluZyIsIm1ldHJpY3MiLCJzZXRNZXRyaWNzIiwibG9hZFRpbWUiLCJyZW5kZXJUaW1lIiwibWVtb3J5VXNhZ2UiLCJwZXJmb3JtYW5jZSIsInRpbWluZyIsImxvYWRFdmVudEVuZCIsIm5hdmlnYXRpb25TdGFydCIsInJlbmRlclN0YXJ0Iiwibm93IiwibWVtb3J5IiwidXNlZEpTSGVhcFNpemUiLCJ0aGVuIiwiZ2V0Q0xTIiwiZ2V0RklEIiwiZ2V0RkNQIiwiZ2V0TENQIiwiZ2V0VFRGQiIsImNvbnNvbGUiLCJsb2ciLCJjYXRjaCIsIk9wdGltaXplZEFuaW1hdGlvbiIsImFuaW1hdGlvbiIsImR1cmF0aW9uIiwiYW5pbWF0aW9ucyIsImZhZGVJbiIsImluaXRpYWwiLCJvcGFjaXR5IiwiYW5pbWF0ZSIsInNsaWRlVXAiLCJ5Iiwic2xpZGVMZWZ0IiwieCIsInNjYWxlIiwidHJhbnNpdGlvbiIsInVzZUltYWdlUHJlbG9hZGVyIiwiaW1hZ2VVcmxzIiwibG9hZGVkSW1hZ2VzIiwic2V0TG9hZGVkSW1hZ2VzIiwiU2V0IiwicHJlbG9hZEltYWdlcyIsInByb21pc2VzIiwidXJsIiwiUHJvbWlzZSIsInJlc29sdmUiLCJyZWplY3QiLCJpbWciLCJ3aW5kb3ciLCJvbmxvYWQiLCJvbmVycm9yIiwibG9hZGVkIiwiYWxsU2V0dGxlZCIsInN1Y2Nlc3NmdWwiLCJyZXN1bHQiLCJzdGF0dXMiLCJCdW5kbGVBbmFseXplciIsInByb2Nlc3MiLCJidW5kbGVJbmZvIiwic2V0QnVuZGxlSW5mbyIsIl9fQlVORExFX0FOQUxZWkVSX18iLCJoNCIsInRvdGFsU2l6ZSIsImNodW5rcyIsIm1vZHVsZXMiLCJQZXJmb3JtYW5jZUJ1ZGdldCIsIndhcm5pbmdzIiwic2V0V2FybmluZ3MiLCJuZXdXYXJuaW5ncyIsInB1c2giLCJ1bCIsIndhcm5pbmciLCJsaSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/performance/PerformanceOptimization.tsx\n");

/***/ }),

/***/ "(ssr)/./components/security/SecurityAccessibility.tsx":
/*!*******************************************************!*\
  !*** ./components/security/SecurityAccessibility.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessibilityToolbar: () => (/* binding */ AccessibilityToolbar),\n/* harmony export */   AriaLiveRegion: () => (/* binding */ AriaLiveRegion),\n/* harmony export */   SecureForm: () => (/* binding */ SecureForm),\n/* harmony export */   SecurityHeaders: () => (/* binding */ SecurityHeaders),\n/* harmony export */   SecurityStatus: () => (/* binding */ SecurityStatus),\n/* harmony export */   SkipToContent: () => (/* binding */ SkipToContent),\n/* harmony export */   useFocusManagement: () => (/* binding */ useFocusManagement),\n/* harmony export */   useSanitizedInput: () => (/* binding */ useSanitizedInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Contrast,Eye,EyeOff,Keyboard,MousePointer,Shield,Type,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Contrast,Eye,EyeOff,Keyboard,MousePointer,Shield,Type,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Contrast,Eye,EyeOff,Keyboard,MousePointer,Shield,Type,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Contrast,Eye,EyeOff,Keyboard,MousePointer,Shield,Type,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/contrast.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Contrast,Eye,EyeOff,Keyboard,MousePointer,Shield,Type,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Contrast,Eye,EyeOff,Keyboard,MousePointer,Shield,Type,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/keyboard.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Contrast,Eye,EyeOff,Keyboard,MousePointer,Shield,Type,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Contrast,Eye,EyeOff,Keyboard,MousePointer,Shield,Type,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Contrast,Eye,EyeOff,Keyboard,MousePointer,Shield,Type,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Contrast,Eye,EyeOff,Keyboard,MousePointer,Shield,Type,Volume2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* __next_internal_client_entry_do_not_use__ SecurityHeaders,useSanitizedInput,AccessibilityToolbar,SecureForm,SecurityStatus,AriaLiveRegion,SkipToContent,useFocusManagement auto */ \n\n\n\n// Security Headers Component\nfunction SecurityHeaders() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Content Security Policy\n        const meta = document.createElement(\"meta\");\n        meta.httpEquiv = \"Content-Security-Policy\";\n        meta.content = \"default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.positive7.in;\";\n        document.head.appendChild(meta);\n        // X-Frame-Options\n        const frameOptions = document.createElement(\"meta\");\n        frameOptions.httpEquiv = \"X-Frame-Options\";\n        frameOptions.content = \"DENY\";\n        document.head.appendChild(frameOptions);\n        // X-Content-Type-Options\n        const contentType = document.createElement(\"meta\");\n        contentType.httpEquiv = \"X-Content-Type-Options\";\n        contentType.content = \"nosniff\";\n        document.head.appendChild(contentType);\n        return ()=>{\n            document.head.removeChild(meta);\n            document.head.removeChild(frameOptions);\n            document.head.removeChild(contentType);\n        };\n    }, []);\n    return null;\n}\n// Input Sanitization Hook\nfunction useSanitizedInput(initialValue = \"\") {\n    const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialValue);\n    const [sanitizedValue, setSanitizedValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialValue);\n    const sanitize = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((input)=>{\n        // Remove potentially dangerous characters and scripts\n        return input.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\").replace(/javascript:/gi, \"\").replace(/on\\w+\\s*=/gi, \"\").replace(/[<>]/g, \"\").trim();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSanitizedValue(sanitize(value));\n    }, [\n        value,\n        sanitize\n    ]);\n    return {\n        value,\n        sanitizedValue,\n        setValue,\n        isValid: value === sanitizedValue\n    };\n}\n// Accessibility Toolbar Component\nfunction AccessibilityToolbar() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        fontSize: 100,\n        contrast: false,\n        screenReader: false,\n        keyboardNav: false,\n        reducedMotion: false\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Apply accessibility settings\n        const root = document.documentElement;\n        // Font size\n        root.style.fontSize = `${settings.fontSize}%`;\n        // High contrast\n        if (settings.contrast) {\n            root.classList.add(\"high-contrast\");\n        } else {\n            root.classList.remove(\"high-contrast\");\n        }\n        // Reduced motion\n        if (settings.reducedMotion) {\n            root.classList.add(\"reduce-motion\");\n        } else {\n            root.classList.remove(\"reduce-motion\");\n        }\n        // Keyboard navigation\n        if (settings.keyboardNav) {\n            root.classList.add(\"keyboard-nav\");\n        } else {\n            root.classList.remove(\"keyboard-nav\");\n        }\n    }, [\n        settings\n    ]);\n    const updateSetting = (key, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"fixed left-4 top-1/2 -translate-y-1/2 z-50 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n                \"aria-label\": \"Open accessibility toolbar\",\n                \"aria-expanded\": isOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        x: -300,\n                        opacity: 0\n                    },\n                    animate: {\n                        x: 0,\n                        opacity: 1\n                    },\n                    exit: {\n                        x: -300,\n                        opacity: 0\n                    },\n                    className: \"fixed left-0 top-0 h-full w-80 bg-white shadow-2xl z-40 overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: \"Accessibility\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsOpen(false),\n                                        className: \"p-2 hover:bg-gray-100 rounded-lg\",\n                                        \"aria-label\": \"Close accessibility toolbar\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4 inline mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Font Size: \",\n                                                    settings.fontSize,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"75\",\n                                                max: \"150\",\n                                                step: \"25\",\n                                                value: settings.fontSize,\n                                                onChange: (e)=>updateSetting(\"fontSize\", parseInt(e.target.value)),\n                                                className: \"w-full\",\n                                                \"aria-label\": \"Adjust font size\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"75%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"100%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"125%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"150%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center text-sm font-medium text-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"High Contrast\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>updateSetting(\"contrast\", !settings.contrast),\n                                                className: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${settings.contrast ? \"bg-blue-600\" : \"bg-gray-200\"}`,\n                                                \"aria-pressed\": settings.contrast,\n                                                \"aria-label\": \"Toggle high contrast mode\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${settings.contrast ? \"translate-x-6\" : \"translate-x-1\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center text-sm font-medium text-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Screen Reader\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>updateSetting(\"screenReader\", !settings.screenReader),\n                                                className: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${settings.screenReader ? \"bg-blue-600\" : \"bg-gray-200\"}`,\n                                                \"aria-pressed\": settings.screenReader,\n                                                \"aria-label\": \"Toggle screen reader support\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${settings.screenReader ? \"translate-x-6\" : \"translate-x-1\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center text-sm font-medium text-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Keyboard Navigation\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>updateSetting(\"keyboardNav\", !settings.keyboardNav),\n                                                className: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${settings.keyboardNav ? \"bg-blue-600\" : \"bg-gray-200\"}`,\n                                                \"aria-pressed\": settings.keyboardNav,\n                                                \"aria-label\": \"Toggle keyboard navigation highlights\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${settings.keyboardNav ? \"translate-x-6\" : \"translate-x-1\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center text-sm font-medium text-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Reduce Motion\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>updateSetting(\"reducedMotion\", !settings.reducedMotion),\n                                                className: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${settings.reducedMotion ? \"bg-blue-600\" : \"bg-gray-200\"}`,\n                                                \"aria-pressed\": settings.reducedMotion,\n                                                \"aria-label\": \"Toggle reduced motion\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${settings.reducedMotion ? \"translate-x-6\" : \"translate-x-1\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSettings({\n                                                fontSize: 100,\n                                                contrast: false,\n                                                screenReader: false,\n                                                keyboardNav: false,\n                                                reducedMotion: false\n                                            }),\n                                        className: \"w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-lg transition-colors\",\n                                        children: \"Reset to Default\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-25 z-30\",\n                onClick: ()=>setIsOpen(false),\n                \"aria-hidden\": \"true\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                lineNumber: 289,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\nfunction SecureForm({ onSubmit, children, className = \"\" }) {\n    const formRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [csrfToken, setCsrfToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Generate CSRF token\n        const token = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n        setCsrfToken(token);\n    }, []);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (isSubmitting) return;\n        setIsSubmitting(true);\n        try {\n            const formData = new FormData(formRef.current);\n            const data = Object.fromEntries(formData.entries());\n            // Add CSRF token\n            data.csrfToken = csrfToken;\n            // Validate and sanitize data\n            const sanitizedData = Object.keys(data).reduce((acc, key)=>{\n                if (typeof data[key] === \"string\") {\n                    acc[key] = data[key].replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\").replace(/javascript:/gi, \"\").replace(/on\\w+\\s*=/gi, \"\").trim();\n                } else {\n                    acc[key] = data[key];\n                }\n                return acc;\n            }, {});\n            await onSubmit(sanitizedData);\n        } catch (error) {\n            console.error(\"Form submission error:\", error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        ref: formRef,\n        onSubmit: handleSubmit,\n        className: className,\n        noValidate: true,\n        autoComplete: \"on\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"hidden\",\n                name: \"csrfToken\",\n                value: csrfToken\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n        lineNumber: 355,\n        columnNumber: 5\n    }, this);\n}\n// Security Status Component\nfunction SecurityStatus() {\n    const [securityChecks, setSecurityChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        https: false,\n        csp: false,\n        xframe: false,\n        nosniff: false,\n        hsts: false\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check HTTPS\n        setSecurityChecks((prev)=>({\n                ...prev,\n                https: window.location.protocol === \"https:\"\n            }));\n        // Check CSP\n        const cspMeta = document.querySelector('meta[http-equiv=\"Content-Security-Policy\"]');\n        setSecurityChecks((prev)=>({\n                ...prev,\n                csp: !!cspMeta\n            }));\n        // Check X-Frame-Options\n        const frameMeta = document.querySelector('meta[http-equiv=\"X-Frame-Options\"]');\n        setSecurityChecks((prev)=>({\n                ...prev,\n                xframe: !!frameMeta\n            }));\n        // Check X-Content-Type-Options\n        const nosniffMeta = document.querySelector('meta[http-equiv=\"X-Content-Type-Options\"]');\n        setSecurityChecks((prev)=>({\n                ...prev,\n                nosniff: !!nosniffMeta\n            }));\n    }, []);\n    if (false) {}\n    const allSecure = Object.values(securityChecks).every(Boolean);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 left-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-xs\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: `w-5 h-5 ${allSecure ? \"text-green-600\" : \"text-yellow-600\"}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: \"Security Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2 text-sm\",\n                children: Object.entries(securityChecks).map(([check, passed])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 capitalize\",\n                                children: check.replace(/([A-Z])/g, \" $1\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 13\n                            }, this),\n                            passed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-4 h-4 text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Contrast_Eye_EyeOff_Keyboard_MousePointer_Shield_Type_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-4 h-4 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, check, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n                lineNumber: 420,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n        lineNumber: 414,\n        columnNumber: 5\n    }, this);\n}\n// ARIA Live Region for Screen Readers\nfunction AriaLiveRegion() {\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [politeness, setPoliteness] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"polite\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Global function to announce messages to screen readers\n        window.announceToScreenReader = (msg, urgent = false)=>{\n            setPoliteness(urgent ? \"assertive\" : \"polite\");\n            setMessage(msg);\n            // Clear message after announcement\n            setTimeout(()=>setMessage(\"\"), 1000);\n        };\n        return ()=>{\n            delete window.announceToScreenReader;\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"aria-live\": politeness,\n        \"aria-atomic\": \"true\",\n        className: \"sr-only\",\n        children: message\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n        lineNumber: 457,\n        columnNumber: 5\n    }, this);\n}\n// Skip to Content Link\nfunction SkipToContent() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: \"#main-content\",\n        className: \"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n        children: \"Skip to main content\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\security\\\\SecurityAccessibility.tsx\",\n        lineNumber: 470,\n        columnNumber: 5\n    }, this);\n}\n// Focus Management Hook\nfunction useFocusManagement() {\n    const focusRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const setFocus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (focusRef.current) {\n            focusRef.current.focus();\n        }\n    }, []);\n    const trapFocus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.key !== \"Tab\") return;\n        const focusableElements = focusRef.current?.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])');\n        if (!focusableElements || focusableElements.length === 0) return;\n        const firstElement = focusableElements[0];\n        const lastElement = focusableElements[focusableElements.length - 1];\n        if (e.shiftKey) {\n            if (document.activeElement === firstElement) {\n                e.preventDefault();\n                lastElement.focus();\n            }\n        } else {\n            if (document.activeElement === lastElement) {\n                e.preventDefault();\n                firstElement.focus();\n            }\n        }\n    }, []);\n    return {\n        focusRef,\n        setFocus,\n        trapFocus\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/security/SecurityAccessibility.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/Button.tsx":
/*!**********************************!*\
  !*** ./components/ui/Button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LoadingSpinner */ \"(ssr)/./components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst variantClasses = {\n    primary: \"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500\",\n    secondary: \"bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500\",\n    outline: \"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500\",\n    ghost: \"text-gray-700 hover:bg-gray-100 focus:ring-primary-500\"\n};\nconst sizeClasses = {\n    sm: \"px-3 py-2 text-sm\",\n    md: \"px-4 py-2 text-sm\",\n    lg: \"px-6 py-3 text-base\"\n};\nfunction Button({ variant = \"primary\", size = \"md\", loading = false, disabled, children, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n        whileHover: {\n            scale: disabled || loading ? 1 : 1.02\n        },\n        whileTap: {\n            scale: disabled || loading ? 1 : 0.98\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none\", variantClasses[variant], sizeClasses[size], className),\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                size: \"sm\",\n                color: variant === \"outline\" || variant === \"ghost\" ? \"gray\" : \"white\",\n                className: \"mr-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/LoadingSpinner.tsx":
/*!******************************************!*\
  !*** ./components/ui/LoadingSpinner.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst sizeClasses = {\n    sm: \"h-4 w-4\",\n    md: \"h-6 w-6\",\n    lg: \"h-8 w-8\",\n    xl: \"h-12 w-12\"\n};\nconst colorClasses = {\n    primary: \"border-primary-600\",\n    secondary: \"border-secondary-600\",\n    white: \"border-white\",\n    gray: \"border-gray-600\"\n};\nfunction LoadingSpinner({ size = \"md\", color = \"primary\", className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        animate: {\n            rotate: 360\n        },\n        transition: {\n            duration: 1,\n            repeat: Infinity,\n            ease: \"linear\"\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-2 border-t-transparent rounded-full\", sizeClasses[size], colorClasses[color], className)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL0xvYWRpbmdTcGlubmVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUUwQjtBQUNhO0FBQ047QUFRakMsTUFBTUcsY0FBYztJQUNsQkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtBQUNOO0FBRUEsTUFBTUMsZUFBZTtJQUNuQkMsU0FBUztJQUNUQyxXQUFXO0lBQ1hDLE9BQU87SUFDUEMsTUFBTTtBQUNSO0FBRWUsU0FBU0MsZUFBZSxFQUNyQ0MsT0FBTyxJQUFJLEVBQ1hDLFFBQVEsU0FBUyxFQUNqQkMsU0FBUyxFQUNXO0lBQ3BCLHFCQUNFLDhEQUFDZixpREFBTUEsQ0FBQ2dCLEdBQUc7UUFDVEMsU0FBUztZQUFFQyxRQUFRO1FBQUk7UUFDdkJDLFlBQVk7WUFBRUMsVUFBVTtZQUFHQyxRQUFRQztZQUFVQyxNQUFNO1FBQVM7UUFDNURSLFdBQVdkLDhDQUFFQSxDQUNYLDhDQUNBQyxXQUFXLENBQUNXLEtBQUssRUFDakJOLFlBQVksQ0FBQ08sTUFBTSxFQUNuQkM7Ozs7OztBQUlSIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9zaXRpdmU3LXRvdXJpc20td2Vic2l0ZS8uL2NvbXBvbmVudHMvdWkvTG9hZGluZ1NwaW5uZXIudHN4PzZiMDMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvbGliL3V0aWxzJztcblxuaW50ZXJmYWNlIExvYWRpbmdTcGlubmVyUHJvcHMge1xuICBzaXplPzogJ3NtJyB8ICdtZCcgfCAnbGcnIHwgJ3hsJztcbiAgY29sb3I/OiAncHJpbWFyeScgfCAnc2Vjb25kYXJ5JyB8ICd3aGl0ZScgfCAnZ3JheSc7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbn1cblxuY29uc3Qgc2l6ZUNsYXNzZXMgPSB7XG4gIHNtOiAnaC00IHctNCcsXG4gIG1kOiAnaC02IHctNicsXG4gIGxnOiAnaC04IHctOCcsXG4gIHhsOiAnaC0xMiB3LTEyJyxcbn07XG5cbmNvbnN0IGNvbG9yQ2xhc3NlcyA9IHtcbiAgcHJpbWFyeTogJ2JvcmRlci1wcmltYXJ5LTYwMCcsXG4gIHNlY29uZGFyeTogJ2JvcmRlci1zZWNvbmRhcnktNjAwJyxcbiAgd2hpdGU6ICdib3JkZXItd2hpdGUnLFxuICBncmF5OiAnYm9yZGVyLWdyYXktNjAwJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmdTcGlubmVyKHsgXG4gIHNpemUgPSAnbWQnLCBcbiAgY29sb3IgPSAncHJpbWFyeScsIFxuICBjbGFzc05hbWUgXG59OiBMb2FkaW5nU3Bpbm5lclByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPG1vdGlvbi5kaXZcbiAgICAgIGFuaW1hdGU9e3sgcm90YXRlOiAzNjAgfX1cbiAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDEsIHJlcGVhdDogSW5maW5pdHksIGVhc2U6ICdsaW5lYXInIH19XG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAnYm9yZGVyLTIgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsJyxcbiAgICAgICAgc2l6ZUNsYXNzZXNbc2l6ZV0sXG4gICAgICAgIGNvbG9yQ2xhc3Nlc1tjb2xvcl0sXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwibW90aW9uIiwiY24iLCJzaXplQ2xhc3NlcyIsInNtIiwibWQiLCJsZyIsInhsIiwiY29sb3JDbGFzc2VzIiwicHJpbWFyeSIsInNlY29uZGFyeSIsIndoaXRlIiwiZ3JheSIsIkxvYWRpbmdTcGlubmVyIiwic2l6ZSIsImNvbG9yIiwiY2xhc3NOYW1lIiwiZGl2IiwiYW5pbWF0ZSIsInJvdGF0ZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsInJlcGVhdCIsIkluZmluaXR5IiwiZWFzZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch current session and user data\n    const fetchSession = async ()=>{\n        try {\n            console.log(\"AuthContext: Fetching session\");\n            const response = await fetch(\"/api/auth/session\", {\n                method: \"GET\",\n                credentials: \"include\"\n            });\n            if (!response.ok) {\n                console.log(\"AuthContext: No active session\");\n                setUser(null);\n                setSession(null);\n                return;\n            }\n            const data = await response.json();\n            console.log(\"AuthContext: Session fetched successfully\");\n            setUser(data.user);\n            setSession(data.session);\n        } catch (error) {\n            console.error(\"AuthContext: Error fetching session:\", error);\n            setUser(null);\n            setSession(null);\n        }\n    };\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            console.log(\"AuthContext: Initializing auth\");\n            await fetchSession();\n            setLoading(false);\n        };\n        initializeAuth();\n    }, []);\n    // Sign up function\n    const signUp = async (email, password, userData)=>{\n        try {\n            console.log(\"AuthContext: Attempting signup\");\n            const response = await fetch(\"/api/auth/register\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    email,\n                    password,\n                    fullName: userData.full_name,\n                    phone: userData.phone\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"AuthContext: Signup error:\", data.error);\n                return {\n                    data: null,\n                    error: data.error\n                };\n            }\n            console.log(\"AuthContext: Signup successful\");\n            return {\n                data: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signup network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Sign in function\n    const signIn = async (email, password)=>{\n        try {\n            console.log(\"AuthContext: Attempting signin\");\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"AuthContext: Signin error:\", data.error);\n                return {\n                    data: null,\n                    error: data.error\n                };\n            }\n            console.log(\"AuthContext: Signin successful\");\n            // Update local state\n            setUser(data.user);\n            setSession(data.session);\n            return {\n                data: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signin network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Google sign in function\n    const signInWithGoogle = async ()=>{\n        try {\n            console.log(\"AuthContext: Attempting Google signin\");\n            const response = await fetch(\"/api/auth/google\", {\n                method: \"POST\",\n                credentials: \"include\"\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"AuthContext: Google signin error:\", data.error);\n                return {\n                    data: null,\n                    error: data.error\n                };\n            }\n            console.log(\"AuthContext: Google OAuth URL generated\");\n            // Redirect to Google OAuth\n            window.location.href = data.url;\n            return {\n                data: data,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Google signin network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Sign out function\n    const signOut = async ()=>{\n        try {\n            console.log(\"AuthContext: Attempting signout\");\n            const response = await fetch(\"/api/auth/logout\", {\n                method: \"POST\",\n                credentials: \"include\"\n            });\n            // Clear local state regardless of API response\n            setUser(null);\n            setSession(null);\n            if (!response.ok) {\n                console.warn(\"AuthContext: Signout API error, but local state cleared\");\n            } else {\n                console.log(\"AuthContext: Signout successful\");\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signout network error:\", error);\n            // Still clear local state\n            setUser(null);\n            setSession(null);\n            return {\n                error: null\n            }; // Don't fail signout for network errors\n        }\n    };\n    // Reset password function\n    const resetPassword = async (email)=>{\n        try {\n            console.log(\"AuthContext: Reset password not implemented via API yet\");\n            return {\n                error: \"Password reset functionality will be available soon\"\n            };\n        } catch (error) {\n            return {\n                error: error.message\n            };\n        }\n    };\n    // Update profile function\n    const updateProfile = async (updates)=>{\n        if (!user) {\n            return {\n                data: null,\n                error: \"No user logged in\"\n            };\n        }\n        try {\n            console.log(\"AuthContext: Update profile not implemented via API yet\");\n            return {\n                data: null,\n                error: \"Profile update functionality will be available soon\"\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Refresh user data\n    const refreshUser = async ()=>{\n        console.log(\"AuthContext: Refreshing user data\");\n        await fetchSession();\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        signUp,\n        signIn,\n        signInWithGoogle,\n        signOut,\n        resetPassword,\n        updateProfile,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateTripDuration: () => (/* binding */ calculateTripDuration),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateBookingReference: () => (/* binding */ generateBookingReference),\n/* harmony export */   generateSEODescription: () => (/* binding */ generateSEODescription),\n/* harmony export */   generateSEOTitle: () => (/* binding */ generateSEOTitle),\n/* harmony export */   getDifficultyColor: () => (/* binding */ getDifficultyColor),\n/* harmony export */   getImageUrl: () => (/* binding */ getImageUrl),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   isValidUrl: () => (/* binding */ isValidUrl),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePhone: () => (/* binding */ validatePhone)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = \"INR\") {\n    return new Intl.NumberFormat(\"en-IN\", {\n        style: \"currency\",\n        currency,\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n    }).format(amount);\n}\nfunction formatDate(date, options) {\n    const defaultOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    };\n    return new Intl.DateTimeFormat(\"en-IN\", {\n        ...defaultOptions,\n        ...options\n    }).format(typeof date === \"string\" ? new Date(date) : date);\n}\nfunction formatRelativeTime(date) {\n    const now = new Date();\n    const targetDate = typeof date === \"string\" ? new Date(date) : date;\n    const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);\n    if (diffInSeconds < 60) return \"just now\";\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;\n    return formatDate(targetDate);\n}\nfunction slugify(text) {\n    return text.toLowerCase().replace(/[^\\w\\s-]/g, \"\").replace(/[\\s_-]+/g, \"-\").replace(/^-+|-+$/g, \"\");\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength).replace(/\\s+\\S*$/, \"\") + \"...\";\n}\nfunction generateBookingReference() {\n    const prefix = \"P7\";\n    const timestamp = Date.now().toString().slice(-6);\n    const random = Math.random().toString(36).substring(2, 6).toUpperCase();\n    return `${prefix}${timestamp}${random}`;\n}\nfunction validateEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction validatePhone(phone) {\n    const phoneRegex = /^[+]?[\\d\\s\\-\\(\\)]{10,}$/;\n    return phoneRegex.test(phone);\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\nfunction getImageUrl(path, fallback) {\n    if (!path) return fallback || \"/images/placeholder.jpg\";\n    // If it's already a full URL, return as is\n    if (path.startsWith(\"http\")) return path;\n    // If it's a Supabase storage path\n    if (path.startsWith(\"/storage/\")) {\n        return `${\"https://soaoagcuubtzojytoati.supabase.co\"}${path}`;\n    }\n    // If it's a relative path, make it absolute\n    if (path.startsWith(\"/\")) return path;\n    return `/${path}`;\n}\nfunction calculateTripDuration(startDate, endDate) {\n    const start = new Date(startDate);\n    const end = new Date(endDate);\n    const diffTime = Math.abs(end.getTime() - start.getTime());\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n}\nfunction getDifficultyColor(difficulty) {\n    switch(difficulty.toLowerCase()){\n        case \"easy\":\n            return \"text-green-600 bg-green-100\";\n        case \"moderate\":\n            return \"text-yellow-600 bg-yellow-100\";\n        case \"challenging\":\n            return \"text-orange-600 bg-orange-100\";\n        case \"extreme\":\n            return \"text-red-600 bg-red-100\";\n        default:\n            return \"text-gray-600 bg-gray-100\";\n    }\n}\nfunction getStatusColor(status) {\n    switch(status.toLowerCase()){\n        case \"confirmed\":\n        case \"completed\":\n        case \"approved\":\n            return \"text-green-600 bg-green-100\";\n        case \"pending\":\n            return \"text-yellow-600 bg-yellow-100\";\n        case \"cancelled\":\n        case \"rejected\":\n            return \"text-red-600 bg-red-100\";\n        case \"in_progress\":\n            return \"text-blue-600 bg-blue-100\";\n        default:\n            return \"text-gray-600 bg-gray-100\";\n    }\n}\nfunction generateSEOTitle(title, suffix) {\n    const baseSuffix = suffix || \"Positive7 - Educational Tours & Student Travel\";\n    return `${title} | ${baseSuffix}`;\n}\nfunction generateSEODescription(description, maxLength = 160) {\n    return truncateText(description, maxLength);\n}\nfunction isValidUrl(url) {\n    try {\n        new URL(url);\n        return true;\n    } catch  {\n        return false;\n    }\n}\nfunction getInitials(name) {\n    return name.split(\" \").map((word)=>word.charAt(0)).join(\"\").toUpperCase().slice(0, 2);\n}\nfunction sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4be39902af1f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wb3NpdGl2ZTctdG91cmlzbS13ZWJzaXRlLy4vYXBwL2dsb2JhbHMuY3NzP2MyYjAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0YmUzOTkwMmFmMWZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/auth/register/page.tsx":
/*!************************************!*\
  !*** ./app/auth/register/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RegisterPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_auth_RegisterForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/RegisterForm */ \"(rsc)/./components/auth/RegisterForm.tsx\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(rsc)/./components/ui/LoadingSpinner.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Register - Positive7 Educational Tours\",\n    description: \"Create your Positive7 account to book educational tours and access exclusive features.\",\n    keywords: \"register, sign up, create account, Positive7, educational tours\"\n};\nfunction RegisterPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-green-50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        size: \"lg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 11\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_RegisterForm__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\auth\\\\register\\\\page.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\auth\\\\register\\\\page.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/auth/register/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\"],\\\"variable\\\":\\\"--font-poppins\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/constants */ \"(rsc)/./lib/constants.ts\");\n/* harmony import */ var _components_security_SecurityAccessibility__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/security/SecurityAccessibility */ \"(rsc)/./components/security/SecurityAccessibility.tsx\");\n/* harmony import */ var _components_performance_PerformanceOptimization__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/performance/PerformanceOptimization */ \"(rsc)/./components/performance/PerformanceOptimization.tsx\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name} - ${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.tagline}`,\n        template: `%s | ${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name}`\n    },\n    description: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.description,\n    keywords: [\n        \"educational tours\",\n        \"student travel\",\n        \"experiential learning\",\n        \"adventure camps\",\n        \"school trips\",\n        \"Gujarat tourism\",\n        \"positive7\",\n        \"educational trips\",\n        \"student tours\",\n        \"CAS projects\",\n        \"workshops\",\n        \"picnics\"\n    ],\n    authors: [\n        {\n            name: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name,\n            url: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website\n        }\n    ],\n    creator: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name,\n    publisher: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name,\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_IN\",\n        url: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website,\n        siteName: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name,\n        title: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name} - ${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.tagline}`,\n        description: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.description,\n        images: [\n            {\n                url: \"/images/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name} - Educational Tours & Student Travel`\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name} - ${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.tagline}`,\n        description: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.description,\n        images: [\n            \"/images/twitter-image.jpg\"\n        ],\n        creator: \"@positive7ind\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    },\n    category: \"travel\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_8___default().variable)}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"any\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/icon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, maximum-scale=5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"font-sans antialiased\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_security_SecurityAccessibility__WEBPACK_IMPORTED_MODULE_4__.SecurityHeaders, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_security_SecurityAccessibility__WEBPACK_IMPORTED_MODULE_4__.SkipToContent, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex min-h-screen flex-col\",\n                                id: \"main-content\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_security_SecurityAccessibility__WEBPACK_IMPORTED_MODULE_4__.AccessibilityToolbar, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_security_SecurityAccessibility__WEBPACK_IMPORTED_MODULE_4__.AriaLiveRegion, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_security_SecurityAccessibility__WEBPACK_IMPORTED_MODULE_4__.SecurityStatus, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_performance_PerformanceOptimization__WEBPACK_IMPORTED_MODULE_5__.PerformanceBudget, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"TravelAgency\",\n                                name: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name,\n                                description: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.description,\n                                url: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website,\n                                logo: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website}/images/positive7-logo.png`,\n                                image: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website}/images/og-image.jpg`,\n                                telephone: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.phone,\n                                email: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.email,\n                                address: {\n                                    \"@type\": \"PostalAddress\",\n                                    streetAddress: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.address,\n                                    addressLocality: \"Ahmedabad\",\n                                    addressRegion: \"Gujarat\",\n                                    postalCode: \"380015\",\n                                    addressCountry: \"IN\"\n                                },\n                                sameAs: [\n                                    \"https://www.facebook.com/positive7.ind\",\n                                    \"https://www.instagram.com/positive.seven/\",\n                                    \"https://www.youtube.com/channel/UC22w2efe7oZCmEcrU8g2xnw/featured\"\n                                ],\n                                serviceType: [\n                                    \"Educational Tours\",\n                                    \"Student Travel\",\n                                    \"Adventure Camps\",\n                                    \"Experiential Learning\",\n                                    \"School Trips\",\n                                    \"CAS Projects\",\n                                    \"Workshops\"\n                                ],\n                                areaServed: {\n                                    \"@type\": \"Country\",\n                                    name: \"India\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_6___default()), {\n                        id: \"pwa-registration\",\n                        strategy: \"afterInteractive\",\n                        children: `\n            if ('serviceWorker' in navigator) {\n              window.addEventListener('load', function() {\n                navigator.serviceWorker.register('/sw.js')\n                  .then(function(registration) {\n                    console.log('SW registered: ', registration);\n                  })\n                  .catch(function(registrationError) {\n                    console.log('SW registration failed: ', registrationError);\n                  });\n              });\n            }\n          `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/auth/RegisterForm.tsx":
/*!******************************************!*\
  !*** ./components/auth/RegisterForm.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\auth\RegisterForm.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./components/performance/PerformanceOptimization.tsx":
/*!************************************************************!*\
  !*** ./components/performance/PerformanceOptimization.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BundleAnalyzer: () => (/* binding */ e9),
/* harmony export */   LazyLoadSection: () => (/* binding */ e5),
/* harmony export */   MemoizedSearchResults: () => (/* binding */ e4),
/* harmony export */   OptimizedAnimation: () => (/* binding */ e7),
/* harmony export */   OptimizedImage: () => (/* binding */ e0),
/* harmony export */   PerformanceBudget: () => (/* binding */ e10),
/* harmony export */   VirtualScroll: () => (/* binding */ e2),
/* harmony export */   useDebounce: () => (/* binding */ e3),
/* harmony export */   useImagePreloader: () => (/* binding */ e8),
/* harmony export */   useLazyLoad: () => (/* binding */ e1),
/* harmony export */   usePerformanceMonitoring: () => (/* binding */ e6)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\performance\PerformanceOptimization.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\performance\PerformanceOptimization.tsx#OptimizedImage`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\performance\PerformanceOptimization.tsx#useLazyLoad`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\performance\PerformanceOptimization.tsx#VirtualScroll`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\performance\PerformanceOptimization.tsx#useDebounce`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\performance\PerformanceOptimization.tsx#MemoizedSearchResults`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\performance\PerformanceOptimization.tsx#LazyLoadSection`);

const e6 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\performance\PerformanceOptimization.tsx#usePerformanceMonitoring`);

const e7 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\performance\PerformanceOptimization.tsx#OptimizedAnimation`);

const e8 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\performance\PerformanceOptimization.tsx#useImagePreloader`);

const e9 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\performance\PerformanceOptimization.tsx#BundleAnalyzer`);

const e10 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\performance\PerformanceOptimization.tsx#PerformanceBudget`);


/***/ }),

/***/ "(rsc)/./components/security/SecurityAccessibility.tsx":
/*!*******************************************************!*\
  !*** ./components/security/SecurityAccessibility.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AccessibilityToolbar: () => (/* binding */ e2),
/* harmony export */   AriaLiveRegion: () => (/* binding */ e5),
/* harmony export */   SecureForm: () => (/* binding */ e3),
/* harmony export */   SecurityHeaders: () => (/* binding */ e0),
/* harmony export */   SecurityStatus: () => (/* binding */ e4),
/* harmony export */   SkipToContent: () => (/* binding */ e6),
/* harmony export */   useFocusManagement: () => (/* binding */ e7),
/* harmony export */   useSanitizedInput: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\security\SecurityAccessibility.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\security\SecurityAccessibility.tsx#SecurityHeaders`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\security\SecurityAccessibility.tsx#useSanitizedInput`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\security\SecurityAccessibility.tsx#AccessibilityToolbar`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\security\SecurityAccessibility.tsx#SecureForm`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\security\SecurityAccessibility.tsx#SecurityStatus`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\security\SecurityAccessibility.tsx#AriaLiveRegion`);

const e6 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\security\SecurityAccessibility.tsx#SkipToContent`);

const e7 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\security\SecurityAccessibility.tsx#useFocusManagement`);


/***/ }),

/***/ "(rsc)/./components/ui/LoadingSpinner.tsx":
/*!******************************************!*\
  !*** ./components/ui/LoadingSpinner.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\ui\LoadingSpinner.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e1),
/* harmony export */   useAuth: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\contexts\AuthContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\contexts\AuthContext.tsx#useAuth`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\contexts\AuthContext.tsx#AuthProvider`);


/***/ }),

/***/ "(rsc)/./lib/constants.ts":
/*!**************************!*\
  !*** ./lib/constants.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BOOKING_STATUS: () => (/* binding */ BOOKING_STATUS),\n/* harmony export */   COMPANY_INFO: () => (/* binding */ COMPANY_INFO),\n/* harmony export */   CONTACT_FORM_TYPES: () => (/* binding */ CONTACT_FORM_TYPES),\n/* harmony export */   DESTINATIONS: () => (/* binding */ DESTINATIONS),\n/* harmony export */   EDUCATIONAL_EXCELLENCE: () => (/* binding */ EDUCATIONAL_EXCELLENCE),\n/* harmony export */   FEATURED_TRIPS: () => (/* binding */ FEATURED_TRIPS),\n/* harmony export */   NAVIGATION_ITEMS: () => (/* binding */ NAVIGATION_ITEMS),\n/* harmony export */   QUICK_LINKS: () => (/* binding */ QUICK_LINKS),\n/* harmony export */   SAINT_AUGUSTINE_QUOTE: () => (/* binding */ SAINT_AUGUSTINE_QUOTE),\n/* harmony export */   SOCIAL_LINKS: () => (/* binding */ SOCIAL_LINKS),\n/* harmony export */   TESTIMONIALS: () => (/* binding */ TESTIMONIALS),\n/* harmony export */   TRIP_CATEGORIES: () => (/* binding */ TRIP_CATEGORIES),\n/* harmony export */   TRIP_DIFFICULTIES: () => (/* binding */ TRIP_DIFFICULTIES),\n/* harmony export */   UDBHAV_INFO: () => (/* binding */ UDBHAV_INFO),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES)\n/* harmony export */ });\n// Constants based on scraped content from positive7.in\nconst COMPANY_INFO = {\n    name: \"Positive7\",\n    tagline: \"Bring Learning To Life\",\n    heroQuote: \"The Best Way To Be Lost & Found At The Same Time Is To TRAVEL\",\n    description: \"Positive7 is a Gujarat Tourism affiliated outbound experiential learning company organizing, educational trips, students tour, CAS Projects, Picnics, adventure camps & Workshops.\",\n    address: \"904, SHIVALIK HIGHSTREET, LANDMARK: B/S, ITC NARMADA HOTEL MANSI – KESHAVBAUG ROAD ,VASTRAPUR, AHMEDABAD-380015.\",\n    phone: \"+91 78780 05500\",\n    alternatePhone: \"+91 7265005500\",\n    email: \"<EMAIL>\",\n    whatsapp: \"+917878005500\",\n    website: \"https://positive7.in\",\n    logo: \"/images/positive7-logo.svg\"\n};\nconst SOCIAL_LINKS = {\n    facebook: \"https://www.facebook.com/positive7.ind\",\n    instagram: \"https://www.instagram.com/positive.seven/\",\n    youtube: \"https://www.youtube.com/channel/UC22w2efe7oZCmEcrU8g2xnw/featured\",\n    whatsapp: \"http://wa.me/+917878005500?text=Hi%20i%20have%20enquiry\"\n};\nconst NAVIGATION_ITEMS = [\n    {\n        name: \"Home\",\n        href: \"/\"\n    },\n    {\n        name: \"About\",\n        href: \"/about\"\n    },\n    {\n        name: \"Trips\",\n        href: \"/trips\"\n    },\n    {\n        name: \"Contact\",\n        href: \"/contact\"\n    },\n    {\n        name: \"Rural Initiative\",\n        href: \"/rural-initiative\"\n    }\n];\nconst QUICK_LINKS = [\n    {\n        name: \"About Us\",\n        href: \"/about\"\n    },\n    {\n        name: \"Gallery\",\n        href: \"/gallery\"\n    },\n    {\n        name: \"Blog\",\n        href: \"/blog\"\n    },\n    {\n        name: \"Trips Photos\",\n        href: \"/trips-photos\"\n    },\n    {\n        name: \"Terms & Conditions\",\n        href: \"/terms-conditions\"\n    },\n    {\n        name: \"Privacy Policy\",\n        href: \"/privacy-policy\"\n    }\n];\n// Scraped trip data from positive7.in\nconst FEATURED_TRIPS = [\n    {\n        id: \"manali\",\n        title: \"Manali\",\n        duration: \"9 Days 8 Nights\",\n        description: \"The most popular hill stations in Himachal, Manali offers the most magnificent views of the Pir Panjal and the Dhauladhar ranges covered with snow for most of the year.\",\n        image: \"https://positive7.in/wp-content/uploads/2025/01/gettyimages-**********-612x612-1.jpg\",\n        difficulty: \"moderate\",\n        category: \"Hill Station\",\n        destination: \"Himachal Pradesh\"\n    },\n    {\n        id: \"rishikesh\",\n        title: \"Rishikesh\",\n        duration: \"7 Days 6 Nights\",\n        description: 'Rishikesh, nestled in the foothills of the Himalayas along the banks of the Ganges River, is a captivating destination known as the \"Yoga Capital of the World\"',\n        image: \"https://positive7.in/wp-content/uploads/2022/09/dusk-time-rishikesh-holy-town-travel-destination-india-1024x684.jpg\",\n        difficulty: \"easy\",\n        category: \"Spiritual\",\n        destination: \"Uttarakhand\"\n    },\n    {\n        id: \"tirthan-valley\",\n        title: \"Tirthan Valley & Jibhi\",\n        duration: \"9 Days 8 Nights\",\n        description: \"Tirthan Valley: Serene Himalayan retreat with lush landscapes and access to the Great Himalayan National Park.\",\n        image: \"https://positive7.in/wp-content/uploads/2024/11/TIRTHAN-VALLEY-JIBHI-1024x697.webp\",\n        difficulty: \"moderate\",\n        category: \"Nature\",\n        destination: \"Himachal Pradesh\"\n    },\n    {\n        id: \"dharamshala\",\n        title: \"Dharamshala\",\n        duration: \"10 Days 9 Nights\",\n        description: \"Amritsar offers culture and history, Dharamshala provides Tibetan serenity, and Dalhousie delights with colonial charm and scenic beauty.\",\n        image: \"https://positive7.in/wp-content/uploads/2024/11/AMRITSAR-DHARAMSHALA-MCLEODGANJ-TRIUND-DALHOUSIE.webp\",\n        difficulty: \"moderate\",\n        category: \"Cultural\",\n        destination: \"Punjab & Himachal Pradesh\"\n    },\n    {\n        id: \"rajpura\",\n        title: \"Rajpura\",\n        duration: \"3 Days 2 Nights\",\n        description: \"Sundha Mata (Rajpura) is a small village located in Jalore district of Rajasthan. It is 64 km away from Mount Abu. This place is famous for Sundha Mata temple.\",\n        image: \"https://positive7.in/wp-content/uploads/2025/04/1602740643_Rajasthan_Adventure_Resort1.webp\",\n        difficulty: \"easy\",\n        category: \"Religious\",\n        destination: \"Rajasthan\"\n    },\n    {\n        id: \"brigu-lake\",\n        title: \"Brigu Lake\",\n        duration: \"9 Days 8 Nights\",\n        description: \"The Brigu Lake trek, located near Manali in Himachal Pradesh, is a stunning adventure that takes you through lush forests, picturesque meadows, and breathtaking mountain views.\",\n        image: \"https://positive7.in/wp-content/uploads/2024/11/BRIGU-LAKE2.webp\",\n        difficulty: \"challenging\",\n        category: \"Trekking\",\n        destination: \"Himachal Pradesh\"\n    }\n];\n// Scraped testimonials from positive7.in\nconst TESTIMONIALS = [\n    {\n        id: 1,\n        name: \"Krupa Bhatt\",\n        role: \"Student\",\n        content: \"If i could rewind those moments those days those experiences again then I surely would may it get less adventures may it get less thrilling and fun but still I would because the past 6 days were a whole sum of adventure and an unforgettable piece of my life.\",\n        rating: 5,\n        image: \"https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop\"\n    },\n    {\n        id: 2,\n        name: \"Kavita Pillai\",\n        role: \"Parent\",\n        content: \"Trekking transforms lives, I had heard this, but for me, I can see those changes in my Son, he has impacted greatly, The transformations has been profound, he loved the trekking experience of his camping trip to Manali with Team Positive 7\",\n        rating: 5,\n        image: \"https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop\"\n    },\n    {\n        id: 3,\n        name: \"Hetal Vora\",\n        role: \"Parent\",\n        content: \"Kids had fun. The coordinators, arrangements, activities, stay place was planned where kids can have maximum enjoyment. Definitely recommended even kid has never stay away from parents for a day.\",\n        rating: 5,\n        image: \"https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop\"\n    },\n    {\n        id: 4,\n        name: \"Sachin Mehta\",\n        role: \"Parent\",\n        content: \"Positive7 is a place of positivity and encouragement. The trip is well organized and has comfortable journey throughout. The activities are very enthusiastic and cheering and constant updates are given to the parents about the trip and the children.\",\n        rating: 5,\n        image: \"https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop\"\n    },\n    {\n        id: 5,\n        name: \"Rani Jaiswal\",\n        role: \"Educator\",\n        content: \"It is a Positive group that spreads positivity in the lives of people connected with it. A wonderful group that gave me beautiful moments to cherish in my life. I got one such good opportunity to be with them during our schl trip with our Student at Borsad, camp dilly.\",\n        rating: 5,\n        image: \"https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop\"\n    },\n    {\n        id: 6,\n        name: \"Shirali Shah\",\n        role: \"Parent\",\n        content: \"Positive7 is such a wonderful team and great example of super team work. Super experience and lot's of fun with discipline. My son learn so much new things. I have send my son for the first time with you and the experience was awesome. Thank you so much.\",\n        rating: 5,\n        image: \"https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop\"\n    }\n];\nconst TRIP_CATEGORIES = [\n    \"Hill Station\",\n    \"Spiritual\",\n    \"Nature\",\n    \"Cultural\",\n    \"Religious\",\n    \"Trekking\",\n    \"Adventure\",\n    \"Wildlife\",\n    \"Historical\"\n];\nconst TRIP_DIFFICULTIES = [\n    {\n        value: \"easy\",\n        label: \"Easy\",\n        color: \"green\"\n    },\n    {\n        value: \"moderate\",\n        label: \"Moderate\",\n        color: \"yellow\"\n    },\n    {\n        value: \"challenging\",\n        label: \"Challenging\",\n        color: \"orange\"\n    },\n    {\n        value: \"extreme\",\n        label: \"Extreme\",\n        color: \"red\"\n    }\n];\nconst DESTINATIONS = [\n    \"Himachal Pradesh\",\n    \"Uttarakhand\",\n    \"Rajasthan\",\n    \"Punjab\",\n    \"Gujarat\",\n    \"Maharashtra\",\n    \"Goa\",\n    \"Kerala\",\n    \"Karnataka\",\n    \"Tamil Nadu\"\n];\nconst UDBHAV_INFO = {\n    title: \"Udbhav: Exploring Rural Life\",\n    description: 'Taking inspiration from these quotes we at \"Positive7\" have come up with an initiative known as \"Udbhav\". It will be a drive to connect the rural and urban areas through culture, art and traditions.',\n    images: [\n        \"https://positive7.in/wp-content/uploads/2022/07/Udbhav.jpg\",\n        \"https://positive7.in/wp-content/uploads/2022/07/Udbhav-2-scaled.jpg\",\n        \"https://positive7.in/wp-content/uploads/2022/07/Udbhav-1-scaled.jpg\",\n        \"https://positive7.in/wp-content/uploads/2022/07/Udbhav-3-1024x467.jpg\"\n    ]\n};\nconst SAINT_AUGUSTINE_QUOTE = {\n    text: '\"The world is a book, and those who do not travel read only one page.\"',\n    author: \"Saint Augustine\",\n    image: \"https://positive7.in/wp-content/uploads/2018/11/quote-1.png\"\n};\nconst EDUCATIONAL_EXCELLENCE = {\n    title: \"Educational Excellence\",\n    description: \"We believe it's not just the exposure to new places that changes student's lives, but also the kind of experience they have during that exposure. That's why we work with you to build programme content that meets your travel / learning goals.\"\n};\nconst CONTACT_FORM_TYPES = [\n    \"General Inquiry\",\n    \"Trip Booking\",\n    \"Custom Trip Request\",\n    \"Group Booking\",\n    \"Educational Program\",\n    \"Udbhav Initiative\",\n    \"Partnership\",\n    \"Other\"\n];\nconst BOOKING_STATUS = {\n    PENDING: \"pending\",\n    CONFIRMED: \"confirmed\",\n    CANCELLED: \"cancelled\",\n    COMPLETED: \"completed\"\n};\nconst USER_ROLES = {\n    CUSTOMER: \"customer\",\n    ADMIN: \"admin\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvY29uc3RhbnRzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHVEQUF1RDtBQUVoRCxNQUFNQSxlQUFlO0lBQzFCQyxNQUFNO0lBQ05DLFNBQVM7SUFDVEMsV0FBVztJQUNYQyxhQUFhO0lBQ2JDLFNBQVM7SUFDVEMsT0FBTztJQUNQQyxnQkFBZ0I7SUFDaEJDLE9BQU87SUFDUEMsVUFBVTtJQUNWQyxTQUFTO0lBQ1RDLE1BQU07QUFDUixFQUFFO0FBRUssTUFBTUMsZUFBZTtJQUMxQkMsVUFBVTtJQUNWQyxXQUFXO0lBQ1hDLFNBQVM7SUFDVE4sVUFBVTtBQUNaLEVBQUU7QUFFSyxNQUFNTyxtQkFBbUI7SUFDOUI7UUFBRWYsTUFBTTtRQUFRZ0IsTUFBTTtJQUFJO0lBQzFCO1FBQUVoQixNQUFNO1FBQVNnQixNQUFNO0lBQVM7SUFDaEM7UUFBRWhCLE1BQU07UUFBU2dCLE1BQU07SUFBUztJQUNoQztRQUFFaEIsTUFBTTtRQUFXZ0IsTUFBTTtJQUFXO0lBQ3BDO1FBQUVoQixNQUFNO1FBQW9CZ0IsTUFBTTtJQUFvQjtDQUN2RCxDQUFDO0FBRUssTUFBTUMsY0FBYztJQUN6QjtRQUFFakIsTUFBTTtRQUFZZ0IsTUFBTTtJQUFTO0lBQ25DO1FBQUVoQixNQUFNO1FBQVdnQixNQUFNO0lBQVc7SUFDcEM7UUFBRWhCLE1BQU07UUFBUWdCLE1BQU07SUFBUTtJQUM5QjtRQUFFaEIsTUFBTTtRQUFnQmdCLE1BQU07SUFBZ0I7SUFDOUM7UUFBRWhCLE1BQU07UUFBc0JnQixNQUFNO0lBQW9CO0lBQ3hEO1FBQUVoQixNQUFNO1FBQWtCZ0IsTUFBTTtJQUFrQjtDQUNuRCxDQUFDO0FBRUYsc0NBQXNDO0FBQy9CLE1BQU1FLGlCQUFpQjtJQUM1QjtRQUNFQyxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsVUFBVTtRQUNWbEIsYUFBYTtRQUNibUIsT0FBTztRQUNQQyxZQUFZO1FBQ1pDLFVBQVU7UUFDVkMsYUFBYTtJQUNmO0lBQ0E7UUFDRU4sSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVmxCLGFBQWE7UUFDYm1CLE9BQU87UUFDUEMsWUFBWTtRQUNaQyxVQUFVO1FBQ1ZDLGFBQWE7SUFDZjtJQUNBO1FBQ0VOLElBQUk7UUFDSkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZsQixhQUFhO1FBQ2JtQixPQUFPO1FBQ1BDLFlBQVk7UUFDWkMsVUFBVTtRQUNWQyxhQUFhO0lBQ2Y7SUFDQTtRQUNFTixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsVUFBVTtRQUNWbEIsYUFBYTtRQUNibUIsT0FBTztRQUNQQyxZQUFZO1FBQ1pDLFVBQVU7UUFDVkMsYUFBYTtJQUNmO0lBQ0E7UUFDRU4sSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVmxCLGFBQWE7UUFDYm1CLE9BQU87UUFDUEMsWUFBWTtRQUNaQyxVQUFVO1FBQ1ZDLGFBQWE7SUFDZjtJQUNBO1FBQ0VOLElBQUk7UUFDSkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZsQixhQUFhO1FBQ2JtQixPQUFPO1FBQ1BDLFlBQVk7UUFDWkMsVUFBVTtRQUNWQyxhQUFhO0lBQ2Y7Q0FDRCxDQUFDO0FBRUYseUNBQXlDO0FBQ2xDLE1BQU1DLGVBQWU7SUFDMUI7UUFDRVAsSUFBSTtRQUNKbkIsTUFBTTtRQUNOMkIsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFFBQVE7UUFDUlAsT0FBTztJQUNUO0lBQ0E7UUFDRUgsSUFBSTtRQUNKbkIsTUFBTTtRQUNOMkIsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFFBQVE7UUFDUlAsT0FBTztJQUNUO0lBQ0E7UUFDRUgsSUFBSTtRQUNKbkIsTUFBTTtRQUNOMkIsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFFBQVE7UUFDUlAsT0FBTztJQUNUO0lBQ0E7UUFDRUgsSUFBSTtRQUNKbkIsTUFBTTtRQUNOMkIsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFFBQVE7UUFDUlAsT0FBTztJQUNUO0lBQ0E7UUFDRUgsSUFBSTtRQUNKbkIsTUFBTTtRQUNOMkIsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFFBQVE7UUFDUlAsT0FBTztJQUNUO0lBQ0E7UUFDRUgsSUFBSTtRQUNKbkIsTUFBTTtRQUNOMkIsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFFBQVE7UUFDUlAsT0FBTztJQUNUO0NBQ0QsQ0FBQztBQUVLLE1BQU1RLGtCQUFrQjtJQUM3QjtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7Q0FDRCxDQUFDO0FBRUssTUFBTUMsb0JBQW9CO0lBQy9CO1FBQUVDLE9BQU87UUFBUUMsT0FBTztRQUFRQyxPQUFPO0lBQVE7SUFDL0M7UUFBRUYsT0FBTztRQUFZQyxPQUFPO1FBQVlDLE9BQU87SUFBUztJQUN4RDtRQUFFRixPQUFPO1FBQWVDLE9BQU87UUFBZUMsT0FBTztJQUFTO0lBQzlEO1FBQUVGLE9BQU87UUFBV0MsT0FBTztRQUFXQyxPQUFPO0lBQU07Q0FDcEQsQ0FBQztBQUVLLE1BQU1DLGVBQWU7SUFDMUI7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7Q0FDRCxDQUFDO0FBRUssTUFBTUMsY0FBYztJQUN6QmhCLE9BQU87SUFDUGpCLGFBQWE7SUFDYmtDLFFBQVE7UUFDTjtRQUNBO1FBQ0E7UUFDQTtLQUNEO0FBQ0gsRUFBRTtBQUVLLE1BQU1DLHdCQUF3QjtJQUNuQ0MsTUFBTTtJQUNOQyxRQUFRO0lBQ1JsQixPQUFPO0FBQ1QsRUFBRTtBQUVLLE1BQU1tQix5QkFBeUI7SUFDcENyQixPQUFPO0lBQ1BqQixhQUFhO0FBQ2YsRUFBRTtBQUVLLE1BQU11QyxxQkFBcUI7SUFDaEM7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtDQUNELENBQUM7QUFFSyxNQUFNQyxpQkFBaUI7SUFDNUJDLFNBQVM7SUFDVEMsV0FBVztJQUNYQyxXQUFXO0lBQ1hDLFdBQVc7QUFDYixFQUFXO0FBRUosTUFBTUMsYUFBYTtJQUN4QkMsVUFBVTtJQUNWQyxPQUFPO0FBQ1QsRUFBVyIsInNvdXJjZXMiOlsid2VicGFjazovL3Bvc2l0aXZlNy10b3VyaXNtLXdlYnNpdGUvLi9saWIvY29uc3RhbnRzLnRzPzA3OGUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29uc3RhbnRzIGJhc2VkIG9uIHNjcmFwZWQgY29udGVudCBmcm9tIHBvc2l0aXZlNy5pblxuXG5leHBvcnQgY29uc3QgQ09NUEFOWV9JTkZPID0ge1xuICBuYW1lOiAnUG9zaXRpdmU3JyxcbiAgdGFnbGluZTogJ0JyaW5nIExlYXJuaW5nIFRvIExpZmUnLFxuICBoZXJvUXVvdGU6ICdUaGUgQmVzdCBXYXkgVG8gQmUgTG9zdCAmIEZvdW5kIEF0IFRoZSBTYW1lIFRpbWUgSXMgVG8gVFJBVkVMJyxcbiAgZGVzY3JpcHRpb246ICdQb3NpdGl2ZTcgaXMgYSBHdWphcmF0IFRvdXJpc20gYWZmaWxpYXRlZCBvdXRib3VuZCBleHBlcmllbnRpYWwgbGVhcm5pbmcgY29tcGFueSBvcmdhbml6aW5nLCBlZHVjYXRpb25hbCB0cmlwcywgc3R1ZGVudHMgdG91ciwgQ0FTIFByb2plY3RzLCBQaWNuaWNzLCBhZHZlbnR1cmUgY2FtcHMgJiBXb3Jrc2hvcHMuJyxcbiAgYWRkcmVzczogJzkwNCwgU0hJVkFMSUsgSElHSFNUUkVFVCwgTEFORE1BUks6IEIvUywgSVRDIE5BUk1BREEgSE9URUwgTUFOU0kg4oCTIEtFU0hBVkJBVUcgUk9BRCAsVkFTVFJBUFVSLCBBSE1FREFCQUQtMzgwMDE1LicsXG4gIHBob25lOiAnKzkxIDc4NzgwIDA1NTAwJyxcbiAgYWx0ZXJuYXRlUGhvbmU6ICcrOTEgNzI2NTAwNTUwMCcsXG4gIGVtYWlsOiAnY29udGFjdEBwb3NpdGl2ZTcuaW4nLFxuICB3aGF0c2FwcDogJys5MTc4NzgwMDU1MDAnLFxuICB3ZWJzaXRlOiAnaHR0cHM6Ly9wb3NpdGl2ZTcuaW4nLFxuICBsb2dvOiAnL2ltYWdlcy9wb3NpdGl2ZTctbG9nby5zdmcnLFxufTtcblxuZXhwb3J0IGNvbnN0IFNPQ0lBTF9MSU5LUyA9IHtcbiAgZmFjZWJvb2s6ICdodHRwczovL3d3dy5mYWNlYm9vay5jb20vcG9zaXRpdmU3LmluZCcsXG4gIGluc3RhZ3JhbTogJ2h0dHBzOi8vd3d3Lmluc3RhZ3JhbS5jb20vcG9zaXRpdmUuc2V2ZW4vJyxcbiAgeW91dHViZTogJ2h0dHBzOi8vd3d3LnlvdXR1YmUuY29tL2NoYW5uZWwvVUMyMncyZWZlN29aQ21FY3JVOGcyeG53L2ZlYXR1cmVkJyxcbiAgd2hhdHNhcHA6ICdodHRwOi8vd2EubWUvKzkxNzg3ODAwNTUwMD90ZXh0PUhpJTIwaSUyMGhhdmUlMjBlbnF1aXJ5Jyxcbn07XG5cbmV4cG9ydCBjb25zdCBOQVZJR0FUSU9OX0lURU1TID0gW1xuICB7IG5hbWU6ICdIb21lJywgaHJlZjogJy8nIH0sXG4gIHsgbmFtZTogJ0Fib3V0JywgaHJlZjogJy9hYm91dCcgfSxcbiAgeyBuYW1lOiAnVHJpcHMnLCBocmVmOiAnL3RyaXBzJyB9LFxuICB7IG5hbWU6ICdDb250YWN0JywgaHJlZjogJy9jb250YWN0JyB9LFxuICB7IG5hbWU6ICdSdXJhbCBJbml0aWF0aXZlJywgaHJlZjogJy9ydXJhbC1pbml0aWF0aXZlJyB9LFxuXTtcblxuZXhwb3J0IGNvbnN0IFFVSUNLX0xJTktTID0gW1xuICB7IG5hbWU6ICdBYm91dCBVcycsIGhyZWY6ICcvYWJvdXQnIH0sXG4gIHsgbmFtZTogJ0dhbGxlcnknLCBocmVmOiAnL2dhbGxlcnknIH0sXG4gIHsgbmFtZTogJ0Jsb2cnLCBocmVmOiAnL2Jsb2cnIH0sXG4gIHsgbmFtZTogJ1RyaXBzIFBob3RvcycsIGhyZWY6ICcvdHJpcHMtcGhvdG9zJyB9LFxuICB7IG5hbWU6ICdUZXJtcyAmIENvbmRpdGlvbnMnLCBocmVmOiAnL3Rlcm1zLWNvbmRpdGlvbnMnIH0sXG4gIHsgbmFtZTogJ1ByaXZhY3kgUG9saWN5JywgaHJlZjogJy9wcml2YWN5LXBvbGljeScgfSxcbl07XG5cbi8vIFNjcmFwZWQgdHJpcCBkYXRhIGZyb20gcG9zaXRpdmU3LmluXG5leHBvcnQgY29uc3QgRkVBVFVSRURfVFJJUFMgPSBbXG4gIHtcbiAgICBpZDogJ21hbmFsaScsXG4gICAgdGl0bGU6ICdNYW5hbGknLFxuICAgIGR1cmF0aW9uOiAnOSBEYXlzIDggTmlnaHRzJyxcbiAgICBkZXNjcmlwdGlvbjogJ1RoZSBtb3N0IHBvcHVsYXIgaGlsbCBzdGF0aW9ucyBpbiBIaW1hY2hhbCwgTWFuYWxpIG9mZmVycyB0aGUgbW9zdCBtYWduaWZpY2VudCB2aWV3cyBvZiB0aGUgUGlyIFBhbmphbCBhbmQgdGhlIERoYXVsYWRoYXIgcmFuZ2VzIGNvdmVyZWQgd2l0aCBzbm93IGZvciBtb3N0IG9mIHRoZSB5ZWFyLicsXG4gICAgaW1hZ2U6ICdodHRwczovL3Bvc2l0aXZlNy5pbi93cC1jb250ZW50L3VwbG9hZHMvMjAyNS8wMS9nZXR0eWltYWdlcy0xMTM0MDQxNjAxLTYxMng2MTItMS5qcGcnLFxuICAgIGRpZmZpY3VsdHk6ICdtb2RlcmF0ZScsXG4gICAgY2F0ZWdvcnk6ICdIaWxsIFN0YXRpb24nLFxuICAgIGRlc3RpbmF0aW9uOiAnSGltYWNoYWwgUHJhZGVzaCcsXG4gIH0sXG4gIHtcbiAgICBpZDogJ3Jpc2hpa2VzaCcsXG4gICAgdGl0bGU6ICdSaXNoaWtlc2gnLFxuICAgIGR1cmF0aW9uOiAnNyBEYXlzIDYgTmlnaHRzJyxcbiAgICBkZXNjcmlwdGlvbjogJ1Jpc2hpa2VzaCwgbmVzdGxlZCBpbiB0aGUgZm9vdGhpbGxzIG9mIHRoZSBIaW1hbGF5YXMgYWxvbmcgdGhlIGJhbmtzIG9mIHRoZSBHYW5nZXMgUml2ZXIsIGlzIGEgY2FwdGl2YXRpbmcgZGVzdGluYXRpb24ga25vd24gYXMgdGhlIFwiWW9nYSBDYXBpdGFsIG9mIHRoZSBXb3JsZFwiJyxcbiAgICBpbWFnZTogJ2h0dHBzOi8vcG9zaXRpdmU3LmluL3dwLWNvbnRlbnQvdXBsb2Fkcy8yMDIyLzA5L2R1c2stdGltZS1yaXNoaWtlc2gtaG9seS10b3duLXRyYXZlbC1kZXN0aW5hdGlvbi1pbmRpYS0xMDI0eDY4NC5qcGcnLFxuICAgIGRpZmZpY3VsdHk6ICdlYXN5JyxcbiAgICBjYXRlZ29yeTogJ1NwaXJpdHVhbCcsXG4gICAgZGVzdGluYXRpb246ICdVdHRhcmFraGFuZCcsXG4gIH0sXG4gIHtcbiAgICBpZDogJ3RpcnRoYW4tdmFsbGV5JyxcbiAgICB0aXRsZTogJ1RpcnRoYW4gVmFsbGV5ICYgSmliaGknLFxuICAgIGR1cmF0aW9uOiAnOSBEYXlzIDggTmlnaHRzJyxcbiAgICBkZXNjcmlwdGlvbjogJ1RpcnRoYW4gVmFsbGV5OiBTZXJlbmUgSGltYWxheWFuIHJldHJlYXQgd2l0aCBsdXNoIGxhbmRzY2FwZXMgYW5kIGFjY2VzcyB0byB0aGUgR3JlYXQgSGltYWxheWFuIE5hdGlvbmFsIFBhcmsuJyxcbiAgICBpbWFnZTogJ2h0dHBzOi8vcG9zaXRpdmU3LmluL3dwLWNvbnRlbnQvdXBsb2Fkcy8yMDI0LzExL1RJUlRIQU4tVkFMTEVZLUpJQkhJLTEwMjR4Njk3LndlYnAnLFxuICAgIGRpZmZpY3VsdHk6ICdtb2RlcmF0ZScsXG4gICAgY2F0ZWdvcnk6ICdOYXR1cmUnLFxuICAgIGRlc3RpbmF0aW9uOiAnSGltYWNoYWwgUHJhZGVzaCcsXG4gIH0sXG4gIHtcbiAgICBpZDogJ2RoYXJhbXNoYWxhJyxcbiAgICB0aXRsZTogJ0RoYXJhbXNoYWxhJyxcbiAgICBkdXJhdGlvbjogJzEwIERheXMgOSBOaWdodHMnLFxuICAgIGRlc2NyaXB0aW9uOiAnQW1yaXRzYXIgb2ZmZXJzIGN1bHR1cmUgYW5kIGhpc3RvcnksIERoYXJhbXNoYWxhIHByb3ZpZGVzIFRpYmV0YW4gc2VyZW5pdHksIGFuZCBEYWxob3VzaWUgZGVsaWdodHMgd2l0aCBjb2xvbmlhbCBjaGFybSBhbmQgc2NlbmljIGJlYXV0eS4nLFxuICAgIGltYWdlOiAnaHR0cHM6Ly9wb3NpdGl2ZTcuaW4vd3AtY29udGVudC91cGxvYWRzLzIwMjQvMTEvQU1SSVRTQVItREhBUkFNU0hBTEEtTUNMRU9ER0FOSi1UUklVTkQtREFMSE9VU0lFLndlYnAnLFxuICAgIGRpZmZpY3VsdHk6ICdtb2RlcmF0ZScsXG4gICAgY2F0ZWdvcnk6ICdDdWx0dXJhbCcsXG4gICAgZGVzdGluYXRpb246ICdQdW5qYWIgJiBIaW1hY2hhbCBQcmFkZXNoJyxcbiAgfSxcbiAge1xuICAgIGlkOiAncmFqcHVyYScsXG4gICAgdGl0bGU6ICdSYWpwdXJhJyxcbiAgICBkdXJhdGlvbjogJzMgRGF5cyAyIE5pZ2h0cycsXG4gICAgZGVzY3JpcHRpb246ICdTdW5kaGEgTWF0YSAoUmFqcHVyYSkgaXMgYSBzbWFsbCB2aWxsYWdlIGxvY2F0ZWQgaW4gSmFsb3JlIGRpc3RyaWN0IG9mIFJhamFzdGhhbi4gSXQgaXMgNjQga20gYXdheSBmcm9tIE1vdW50IEFidS4gVGhpcyBwbGFjZSBpcyBmYW1vdXMgZm9yIFN1bmRoYSBNYXRhIHRlbXBsZS4nLFxuICAgIGltYWdlOiAnaHR0cHM6Ly9wb3NpdGl2ZTcuaW4vd3AtY29udGVudC91cGxvYWRzLzIwMjUvMDQvMTYwMjc0MDY0M19SYWphc3RoYW5fQWR2ZW50dXJlX1Jlc29ydDEud2VicCcsXG4gICAgZGlmZmljdWx0eTogJ2Vhc3knLFxuICAgIGNhdGVnb3J5OiAnUmVsaWdpb3VzJyxcbiAgICBkZXN0aW5hdGlvbjogJ1JhamFzdGhhbicsXG4gIH0sXG4gIHtcbiAgICBpZDogJ2JyaWd1LWxha2UnLFxuICAgIHRpdGxlOiAnQnJpZ3UgTGFrZScsXG4gICAgZHVyYXRpb246ICc5IERheXMgOCBOaWdodHMnLFxuICAgIGRlc2NyaXB0aW9uOiAnVGhlIEJyaWd1IExha2UgdHJlaywgbG9jYXRlZCBuZWFyIE1hbmFsaSBpbiBIaW1hY2hhbCBQcmFkZXNoLCBpcyBhIHN0dW5uaW5nIGFkdmVudHVyZSB0aGF0IHRha2VzIHlvdSB0aHJvdWdoIGx1c2ggZm9yZXN0cywgcGljdHVyZXNxdWUgbWVhZG93cywgYW5kIGJyZWF0aHRha2luZyBtb3VudGFpbiB2aWV3cy4nLFxuICAgIGltYWdlOiAnaHR0cHM6Ly9wb3NpdGl2ZTcuaW4vd3AtY29udGVudC91cGxvYWRzLzIwMjQvMTEvQlJJR1UtTEFLRTIud2VicCcsXG4gICAgZGlmZmljdWx0eTogJ2NoYWxsZW5naW5nJyxcbiAgICBjYXRlZ29yeTogJ1RyZWtraW5nJyxcbiAgICBkZXN0aW5hdGlvbjogJ0hpbWFjaGFsIFByYWRlc2gnLFxuICB9LFxuXTtcblxuLy8gU2NyYXBlZCB0ZXN0aW1vbmlhbHMgZnJvbSBwb3NpdGl2ZTcuaW5cbmV4cG9ydCBjb25zdCBURVNUSU1PTklBTFMgPSBbXG4gIHtcbiAgICBpZDogMSxcbiAgICBuYW1lOiAnS3J1cGEgQmhhdHQnLFxuICAgIHJvbGU6ICdTdHVkZW50JyxcbiAgICBjb250ZW50OiAnSWYgaSBjb3VsZCByZXdpbmQgdGhvc2UgbW9tZW50cyB0aG9zZSBkYXlzIHRob3NlIGV4cGVyaWVuY2VzIGFnYWluIHRoZW4gSSBzdXJlbHkgd291bGQgbWF5IGl0IGdldCBsZXNzIGFkdmVudHVyZXMgbWF5IGl0IGdldCBsZXNzIHRocmlsbGluZyBhbmQgZnVuIGJ1dCBzdGlsbCBJIHdvdWxkIGJlY2F1c2UgdGhlIHBhc3QgNiBkYXlzIHdlcmUgYSB3aG9sZSBzdW0gb2YgYWR2ZW50dXJlIGFuZCBhbiB1bmZvcmdldHRhYmxlIHBpZWNlIG9mIG15IGxpZmUuJyxcbiAgICByYXRpbmc6IDUsXG4gICAgaW1hZ2U6ICdodHRwczovL2ltYWdlcy5wZXhlbHMuY29tL3Bob3Rvcy83NzQ5MDkvcGV4ZWxzLXBob3RvLTc3NDkwOS5qcGVnP2F1dG89Y29tcHJlc3MmY3M9dGlueXNyZ2Imdz00MDAmaD00MDAmZml0PWNyb3AnLFxuICB9LFxuICB7XG4gICAgaWQ6IDIsXG4gICAgbmFtZTogJ0thdml0YSBQaWxsYWknLFxuICAgIHJvbGU6ICdQYXJlbnQnLFxuICAgIGNvbnRlbnQ6ICdUcmVra2luZyB0cmFuc2Zvcm1zIGxpdmVzLCBJIGhhZCBoZWFyZCB0aGlzLCBidXQgZm9yIG1lLCBJIGNhbiBzZWUgdGhvc2UgY2hhbmdlcyBpbiBteSBTb24sIGhlIGhhcyBpbXBhY3RlZCBncmVhdGx5LCBUaGUgdHJhbnNmb3JtYXRpb25zIGhhcyBiZWVuIHByb2ZvdW5kLCBoZSBsb3ZlZCB0aGUgdHJla2tpbmcgZXhwZXJpZW5jZSBvZiBoaXMgY2FtcGluZyB0cmlwIHRvIE1hbmFsaSB3aXRoIFRlYW0gUG9zaXRpdmUgNycsXG4gICAgcmF0aW5nOiA1LFxuICAgIGltYWdlOiAnaHR0cHM6Ly9pbWFnZXMucGV4ZWxzLmNvbS9waG90b3MvMTIzOTI5MS9wZXhlbHMtcGhvdG8tMTIzOTI5MS5qcGVnP2F1dG89Y29tcHJlc3MmY3M9dGlueXNyZ2Imdz00MDAmaD00MDAmZml0PWNyb3AnLFxuICB9LFxuICB7XG4gICAgaWQ6IDMsXG4gICAgbmFtZTogJ0hldGFsIFZvcmEnLFxuICAgIHJvbGU6ICdQYXJlbnQnLFxuICAgIGNvbnRlbnQ6ICdLaWRzIGhhZCBmdW4uIFRoZSBjb29yZGluYXRvcnMsIGFycmFuZ2VtZW50cywgYWN0aXZpdGllcywgc3RheSBwbGFjZSB3YXMgcGxhbm5lZCB3aGVyZSBraWRzIGNhbiBoYXZlIG1heGltdW0gZW5qb3ltZW50LiBEZWZpbml0ZWx5IHJlY29tbWVuZGVkIGV2ZW4ga2lkIGhhcyBuZXZlciBzdGF5IGF3YXkgZnJvbSBwYXJlbnRzIGZvciBhIGRheS4nLFxuICAgIHJhdGluZzogNSxcbiAgICBpbWFnZTogJ2h0dHBzOi8vaW1hZ2VzLnBleGVscy5jb20vcGhvdG9zLzQxNTgyOS9wZXhlbHMtcGhvdG8tNDE1ODI5LmpwZWc/YXV0bz1jb21wcmVzcyZjcz10aW55c3JnYiZ3PTQwMCZoPTQwMCZmaXQ9Y3JvcCcsXG4gIH0sXG4gIHtcbiAgICBpZDogNCxcbiAgICBuYW1lOiAnU2FjaGluIE1laHRhJyxcbiAgICByb2xlOiAnUGFyZW50JyxcbiAgICBjb250ZW50OiAnUG9zaXRpdmU3IGlzIGEgcGxhY2Ugb2YgcG9zaXRpdml0eSBhbmQgZW5jb3VyYWdlbWVudC4gVGhlIHRyaXAgaXMgd2VsbCBvcmdhbml6ZWQgYW5kIGhhcyBjb21mb3J0YWJsZSBqb3VybmV5IHRocm91Z2hvdXQuIFRoZSBhY3Rpdml0aWVzIGFyZSB2ZXJ5IGVudGh1c2lhc3RpYyBhbmQgY2hlZXJpbmcgYW5kIGNvbnN0YW50IHVwZGF0ZXMgYXJlIGdpdmVuIHRvIHRoZSBwYXJlbnRzIGFib3V0IHRoZSB0cmlwIGFuZCB0aGUgY2hpbGRyZW4uJyxcbiAgICByYXRpbmc6IDUsXG4gICAgaW1hZ2U6ICdodHRwczovL2ltYWdlcy5wZXhlbHMuY29tL3Bob3Rvcy8yMjA0NTMvcGV4ZWxzLXBob3RvLTIyMDQ1My5qcGVnP2F1dG89Y29tcHJlc3MmY3M9dGlueXNyZ2Imdz00MDAmaD00MDAmZml0PWNyb3AnLFxuICB9LFxuICB7XG4gICAgaWQ6IDUsXG4gICAgbmFtZTogJ1JhbmkgSmFpc3dhbCcsXG4gICAgcm9sZTogJ0VkdWNhdG9yJyxcbiAgICBjb250ZW50OiAnSXQgaXMgYSBQb3NpdGl2ZSBncm91cCB0aGF0IHNwcmVhZHMgcG9zaXRpdml0eSBpbiB0aGUgbGl2ZXMgb2YgcGVvcGxlIGNvbm5lY3RlZCB3aXRoIGl0LiBBIHdvbmRlcmZ1bCBncm91cCB0aGF0IGdhdmUgbWUgYmVhdXRpZnVsIG1vbWVudHMgdG8gY2hlcmlzaCBpbiBteSBsaWZlLiBJIGdvdCBvbmUgc3VjaCBnb29kIG9wcG9ydHVuaXR5IHRvIGJlIHdpdGggdGhlbSBkdXJpbmcgb3VyIHNjaGwgdHJpcCB3aXRoIG91ciBTdHVkZW50IGF0IEJvcnNhZCwgY2FtcCBkaWxseS4nLFxuICAgIHJhdGluZzogNSxcbiAgICBpbWFnZTogJ2h0dHBzOi8vaW1hZ2VzLnBleGVscy5jb20vcGhvdG9zLzExODE2ODYvcGV4ZWxzLXBob3RvLTExODE2ODYuanBlZz9hdXRvPWNvbXByZXNzJmNzPXRpbnlzcmdiJnc9NDAwJmg9NDAwJmZpdD1jcm9wJyxcbiAgfSxcbiAge1xuICAgIGlkOiA2LFxuICAgIG5hbWU6ICdTaGlyYWxpIFNoYWgnLFxuICAgIHJvbGU6ICdQYXJlbnQnLFxuICAgIGNvbnRlbnQ6ICdQb3NpdGl2ZTcgaXMgc3VjaCBhIHdvbmRlcmZ1bCB0ZWFtIGFuZCBncmVhdCBleGFtcGxlIG9mIHN1cGVyIHRlYW0gd29yay4gU3VwZXIgZXhwZXJpZW5jZSBhbmQgbG90XFwncyBvZiBmdW4gd2l0aCBkaXNjaXBsaW5lLiBNeSBzb24gbGVhcm4gc28gbXVjaCBuZXcgdGhpbmdzLiBJIGhhdmUgc2VuZCBteSBzb24gZm9yIHRoZSBmaXJzdCB0aW1lIHdpdGggeW91IGFuZCB0aGUgZXhwZXJpZW5jZSB3YXMgYXdlc29tZS4gVGhhbmsgeW91IHNvIG11Y2guJyxcbiAgICByYXRpbmc6IDUsXG4gICAgaW1hZ2U6ICdodHRwczovL2ltYWdlcy5wZXhlbHMuY29tL3Bob3Rvcy8xMDQzNDcxL3BleGVscy1waG90by0xMDQzNDcxLmpwZWc/YXV0bz1jb21wcmVzcyZjcz10aW55c3JnYiZ3PTQwMCZoPTQwMCZmaXQ9Y3JvcCcsXG4gIH0sXG5dO1xuXG5leHBvcnQgY29uc3QgVFJJUF9DQVRFR09SSUVTID0gW1xuICAnSGlsbCBTdGF0aW9uJyxcbiAgJ1NwaXJpdHVhbCcsXG4gICdOYXR1cmUnLFxuICAnQ3VsdHVyYWwnLFxuICAnUmVsaWdpb3VzJyxcbiAgJ1RyZWtraW5nJyxcbiAgJ0FkdmVudHVyZScsXG4gICdXaWxkbGlmZScsXG4gICdIaXN0b3JpY2FsJyxcbl07XG5cbmV4cG9ydCBjb25zdCBUUklQX0RJRkZJQ1VMVElFUyA9IFtcbiAgeyB2YWx1ZTogJ2Vhc3knLCBsYWJlbDogJ0Vhc3knLCBjb2xvcjogJ2dyZWVuJyB9LFxuICB7IHZhbHVlOiAnbW9kZXJhdGUnLCBsYWJlbDogJ01vZGVyYXRlJywgY29sb3I6ICd5ZWxsb3cnIH0sXG4gIHsgdmFsdWU6ICdjaGFsbGVuZ2luZycsIGxhYmVsOiAnQ2hhbGxlbmdpbmcnLCBjb2xvcjogJ29yYW5nZScgfSxcbiAgeyB2YWx1ZTogJ2V4dHJlbWUnLCBsYWJlbDogJ0V4dHJlbWUnLCBjb2xvcjogJ3JlZCcgfSxcbl07XG5cbmV4cG9ydCBjb25zdCBERVNUSU5BVElPTlMgPSBbXG4gICdIaW1hY2hhbCBQcmFkZXNoJyxcbiAgJ1V0dGFyYWtoYW5kJyxcbiAgJ1JhamFzdGhhbicsXG4gICdQdW5qYWInLFxuICAnR3VqYXJhdCcsXG4gICdNYWhhcmFzaHRyYScsXG4gICdHb2EnLFxuICAnS2VyYWxhJyxcbiAgJ0thcm5hdGFrYScsXG4gICdUYW1pbCBOYWR1Jyxcbl07XG5cbmV4cG9ydCBjb25zdCBVREJIQVZfSU5GTyA9IHtcbiAgdGl0bGU6ICdVZGJoYXY6IEV4cGxvcmluZyBSdXJhbCBMaWZlJyxcbiAgZGVzY3JpcHRpb246ICdUYWtpbmcgaW5zcGlyYXRpb24gZnJvbSB0aGVzZSBxdW90ZXMgd2UgYXQgXCJQb3NpdGl2ZTdcIiBoYXZlIGNvbWUgdXAgd2l0aCBhbiBpbml0aWF0aXZlIGtub3duIGFzIFwiVWRiaGF2XCIuIEl0IHdpbGwgYmUgYSBkcml2ZSB0byBjb25uZWN0IHRoZSBydXJhbCBhbmQgdXJiYW4gYXJlYXMgdGhyb3VnaCBjdWx0dXJlLCBhcnQgYW5kIHRyYWRpdGlvbnMuJyxcbiAgaW1hZ2VzOiBbXG4gICAgJ2h0dHBzOi8vcG9zaXRpdmU3LmluL3dwLWNvbnRlbnQvdXBsb2Fkcy8yMDIyLzA3L1VkYmhhdi5qcGcnLFxuICAgICdodHRwczovL3Bvc2l0aXZlNy5pbi93cC1jb250ZW50L3VwbG9hZHMvMjAyMi8wNy9VZGJoYXYtMi1zY2FsZWQuanBnJyxcbiAgICAnaHR0cHM6Ly9wb3NpdGl2ZTcuaW4vd3AtY29udGVudC91cGxvYWRzLzIwMjIvMDcvVWRiaGF2LTEtc2NhbGVkLmpwZycsXG4gICAgJ2h0dHBzOi8vcG9zaXRpdmU3LmluL3dwLWNvbnRlbnQvdXBsb2Fkcy8yMDIyLzA3L1VkYmhhdi0zLTEwMjR4NDY3LmpwZycsXG4gIF0sXG59O1xuXG5leHBvcnQgY29uc3QgU0FJTlRfQVVHVVNUSU5FX1FVT1RFID0ge1xuICB0ZXh0OiAnXCJUaGUgd29ybGQgaXMgYSBib29rLCBhbmQgdGhvc2Ugd2hvIGRvIG5vdCB0cmF2ZWwgcmVhZCBvbmx5IG9uZSBwYWdlLlwiJyxcbiAgYXV0aG9yOiAnU2FpbnQgQXVndXN0aW5lJyxcbiAgaW1hZ2U6ICdodHRwczovL3Bvc2l0aXZlNy5pbi93cC1jb250ZW50L3VwbG9hZHMvMjAxOC8xMS9xdW90ZS0xLnBuZycsXG59O1xuXG5leHBvcnQgY29uc3QgRURVQ0FUSU9OQUxfRVhDRUxMRU5DRSA9IHtcbiAgdGl0bGU6ICdFZHVjYXRpb25hbCBFeGNlbGxlbmNlJyxcbiAgZGVzY3JpcHRpb246ICdXZSBiZWxpZXZlIGl0XFwncyBub3QganVzdCB0aGUgZXhwb3N1cmUgdG8gbmV3IHBsYWNlcyB0aGF0IGNoYW5nZXMgc3R1ZGVudFxcJ3MgbGl2ZXMsIGJ1dCBhbHNvIHRoZSBraW5kIG9mIGV4cGVyaWVuY2UgdGhleSBoYXZlIGR1cmluZyB0aGF0IGV4cG9zdXJlLiBUaGF0XFwncyB3aHkgd2Ugd29yayB3aXRoIHlvdSB0byBidWlsZCBwcm9ncmFtbWUgY29udGVudCB0aGF0IG1lZXRzIHlvdXIgdHJhdmVsIC8gbGVhcm5pbmcgZ29hbHMuJyxcbn07XG5cbmV4cG9ydCBjb25zdCBDT05UQUNUX0ZPUk1fVFlQRVMgPSBbXG4gICdHZW5lcmFsIElucXVpcnknLFxuICAnVHJpcCBCb29raW5nJyxcbiAgJ0N1c3RvbSBUcmlwIFJlcXVlc3QnLFxuICAnR3JvdXAgQm9va2luZycsXG4gICdFZHVjYXRpb25hbCBQcm9ncmFtJyxcbiAgJ1VkYmhhdiBJbml0aWF0aXZlJyxcbiAgJ1BhcnRuZXJzaGlwJyxcbiAgJ090aGVyJyxcbl07XG5cbmV4cG9ydCBjb25zdCBCT09LSU5HX1NUQVRVUyA9IHtcbiAgUEVORElORzogJ3BlbmRpbmcnLFxuICBDT05GSVJNRUQ6ICdjb25maXJtZWQnLFxuICBDQU5DRUxMRUQ6ICdjYW5jZWxsZWQnLFxuICBDT01QTEVURUQ6ICdjb21wbGV0ZWQnLFxufSBhcyBjb25zdDtcblxuZXhwb3J0IGNvbnN0IFVTRVJfUk9MRVMgPSB7XG4gIENVU1RPTUVSOiAnY3VzdG9tZXInLFxuICBBRE1JTjogJ2FkbWluJyxcbn0gYXMgY29uc3Q7XG4iXSwibmFtZXMiOlsiQ09NUEFOWV9JTkZPIiwibmFtZSIsInRhZ2xpbmUiLCJoZXJvUXVvdGUiLCJkZXNjcmlwdGlvbiIsImFkZHJlc3MiLCJwaG9uZSIsImFsdGVybmF0ZVBob25lIiwiZW1haWwiLCJ3aGF0c2FwcCIsIndlYnNpdGUiLCJsb2dvIiwiU09DSUFMX0xJTktTIiwiZmFjZWJvb2siLCJpbnN0YWdyYW0iLCJ5b3V0dWJlIiwiTkFWSUdBVElPTl9JVEVNUyIsImhyZWYiLCJRVUlDS19MSU5LUyIsIkZFQVRVUkVEX1RSSVBTIiwiaWQiLCJ0aXRsZSIsImR1cmF0aW9uIiwiaW1hZ2UiLCJkaWZmaWN1bHR5IiwiY2F0ZWdvcnkiLCJkZXN0aW5hdGlvbiIsIlRFU1RJTU9OSUFMUyIsInJvbGUiLCJjb250ZW50IiwicmF0aW5nIiwiVFJJUF9DQVRFR09SSUVTIiwiVFJJUF9ESUZGSUNVTFRJRVMiLCJ2YWx1ZSIsImxhYmVsIiwiY29sb3IiLCJERVNUSU5BVElPTlMiLCJVREJIQVZfSU5GTyIsImltYWdlcyIsIlNBSU5UX0FVR1VTVElORV9RVU9URSIsInRleHQiLCJhdXRob3IiLCJFRFVDQVRJT05BTF9FWENFTExFTkNFIiwiQ09OVEFDVF9GT1JNX1RZUEVTIiwiQk9PS0lOR19TVEFUVVMiLCJQRU5ESU5HIiwiQ09ORklSTUVEIiwiQ0FOQ0VMTEVEIiwiQ09NUExFVEVEIiwiVVNFUl9ST0xFUyIsIkNVU1RPTUVSIiwiQURNSU4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/constants.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fregister%2Fpage&page=%2Fauth%2Fregister%2Fpage&appPaths=%2Fauth%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fregister%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();