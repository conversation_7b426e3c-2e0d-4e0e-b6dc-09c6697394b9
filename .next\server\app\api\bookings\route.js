"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/bookings/route";
exports.ids = ["app/api/bookings/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbookings%2Froute&page=%2Fapi%2Fbookings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbookings%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbookings%2Froute&page=%2Fapi%2Fbookings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbookings%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_peebs_Documents_projects_p7_comprehensive_app_api_bookings_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/bookings/route.ts */ \"(rsc)/./app/api/bookings/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/bookings/route\",\n        pathname: \"/api/bookings\",\n        filename: \"route\",\n        bundlePath: \"app/api/bookings/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\api\\\\bookings\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_peebs_Documents_projects_p7_comprehensive_app_api_bookings_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/bookings/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbookings%2Froute&page=%2Fapi%2Fbookings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbookings%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/bookings/route.ts":
/*!***********************************!*\
  !*** ./app/api/bookings/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase-server */ \"(rsc)/./lib/supabase-server.ts\");\n\n\n// Generate unique booking reference\nfunction generateBookingReference() {\n    const prefix = \"P7\";\n    const timestamp = Date.now().toString().slice(-6);\n    const random = Math.random().toString(36).substring(2, 6).toUpperCase();\n    return `${prefix}${timestamp}${random}`;\n}\n// GET /api/bookings - Get bookings with filtering\nasync function GET(request) {\n    try {\n        const session = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.verifySession)();\n        if (!session) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        // Parse query parameters\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const status = searchParams.get(\"status\");\n        const dateFrom = searchParams.get(\"dateFrom\");\n        const dateTo = searchParams.get(\"dateTo\");\n        const userId = searchParams.get(\"userId\");\n        const tripId = searchParams.get(\"tripId\");\n        // Check if user is admin\n        const { data: user } = await supabase.from(\"users\").select(\"role\").eq(\"id\", session.user.id).single();\n        const isAdmin = user?.role === \"admin\";\n        // Build query\n        let query = supabase.from(\"bookings\").select(`\n        *,\n        user:users(id, full_name, email, phone),\n        trip:trips(id, title, destination, duration_days, price_per_person)\n      `, {\n            count: \"exact\"\n        });\n        // Non-admin users can only see their own bookings\n        if (!isAdmin) {\n            query = query.eq(\"user_id\", session.user.id);\n        }\n        // Apply filters\n        if (status) {\n            query = query.eq(\"status\", status);\n        }\n        if (dateFrom) {\n            query = query.gte(\"booking_date\", dateFrom);\n        }\n        if (dateTo) {\n            query = query.lte(\"booking_date\", dateTo);\n        }\n        if (userId && isAdmin) {\n            query = query.eq(\"user_id\", userId);\n        }\n        if (tripId) {\n            query = query.eq(\"trip_id\", tripId);\n        }\n        // Apply pagination\n        const from = (page - 1) * limit;\n        const to = from + limit - 1;\n        query = query.range(from, to);\n        // Order by created_at descending\n        query = query.order(\"created_at\", {\n            ascending: false\n        });\n        const { data: bookings, error, count } = await query;\n        if (error) {\n            console.error(\"Error fetching bookings:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to fetch bookings\"\n            }, {\n                status: 500\n            });\n        }\n        const totalPages = Math.ceil((count || 0) / limit);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            data: bookings,\n            pagination: {\n                page,\n                limit,\n                total: count || 0,\n                totalPages\n            }\n        });\n    } catch (error) {\n        console.error(\"Error in GET /api/bookings:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/bookings - Create a new booking\nasync function POST(request) {\n    try {\n        const session = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.verifySession)();\n        if (!session) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        // Validate required fields\n        const requiredFields = [\n            \"trip_id\",\n            \"number_of_participants\",\n            \"emergency_contact\",\n            \"participants\",\n            \"booking_date\"\n        ];\n        for (const field of requiredFields){\n            if (!body[field]) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: `Missing required field: ${field}`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Validate participants count\n        if (body.participants.length !== body.number_of_participants) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Number of participants does not match participants array length\"\n            }, {\n                status: 400\n            });\n        }\n        // Get trip details and validate availability\n        const { data: trip, error: tripError } = await supabase.from(\"trips\").select(\"*\").eq(\"id\", body.trip_id).eq(\"is_active\", true).single();\n        if (tripError || !trip) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Trip not found or not available\"\n            }, {\n                status: 404\n            });\n        }\n        // Check if trip is available for the booking date\n        if (trip.available_from && body.booking_date < trip.available_from) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Trip is not available for the selected date\"\n            }, {\n                status: 400\n            });\n        }\n        if (trip.available_to && body.booking_date > trip.available_to) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Trip is not available for the selected date\"\n            }, {\n                status: 400\n            });\n        }\n        // Check participant limits\n        if (body.number_of_participants < trip.min_participants) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: `Minimum ${trip.min_participants} participants required`\n            }, {\n                status: 400\n            });\n        }\n        if (body.number_of_participants > trip.max_participants) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: `Maximum ${trip.max_participants} participants allowed`\n            }, {\n                status: 400\n            });\n        }\n        // Check for booking conflicts (same trip, same date)\n        const { data: existingBookings } = await supabase.from(\"bookings\").select(\"number_of_participants\").eq(\"trip_id\", body.trip_id).eq(\"booking_date\", body.booking_date).in(\"status\", [\n            \"pending\",\n            \"confirmed\"\n        ]);\n        const totalBooked = existingBookings?.reduce((sum, booking)=>sum + booking.number_of_participants, 0) || 0;\n        const remainingSlots = trip.max_participants - totalBooked;\n        if (body.number_of_participants > remainingSlots) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: `Only ${remainingSlots} slots available for this date`\n            }, {\n                status: 400\n            });\n        }\n        // Calculate total amount\n        const totalAmount = trip.price_per_person * body.number_of_participants;\n        // Generate booking reference\n        const bookingReference = generateBookingReference();\n        // Create booking\n        const { data: booking, error } = await supabase.from(\"bookings\").insert({\n            user_id: session.user.id,\n            trip_id: body.trip_id,\n            booking_reference: bookingReference,\n            number_of_participants: body.number_of_participants,\n            total_amount: totalAmount,\n            special_requirements: body.special_requirements,\n            emergency_contact: body.emergency_contact,\n            participants: body.participants,\n            booking_date: body.booking_date,\n            status: \"pending\",\n            payment_details: {\n                method: \"pending\",\n                payment_status: \"pending\",\n                amount_paid: 0\n            }\n        }).select(`\n        *,\n        user:users(id, full_name, email, phone),\n        trip:trips(id, title, destination, duration_days, price_per_person)\n      `).single();\n        if (error) {\n            console.error(\"Error creating booking:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to create booking\"\n            }, {\n                status: 500\n            });\n        }\n        // TODO: Send confirmation email\n        // TODO: Create payment intent with payment gateway\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            data: booking,\n            message: \"Booking created successfully\"\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error in POST /api/bookings:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/bookings/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase-server.ts":
/*!********************************!*\
  !*** ./lib/supabase-server.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabase: () => (/* binding */ createServerSupabase),\n/* harmony export */   verifySession: () => (/* binding */ verifySession)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Environment variables with fallbacks for development\nconst supabaseUrl = \"https://soaoagcuubtzojytoati.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNvYW9hZ2N1dWJ0em9qeXRvYXRpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0ODQyOTksImV4cCI6MjA2NDA2MDI5OX0.h0NpruyXbMY9bN-RB_Ng_s_uscP6G3VW_R0rM91DtW0\" || 0;\n// Server-side client for API routes, Server Components, and Server Actions\nconst createServerSupabase = ()=>{\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n};\n// Server-side session verification function\nconst verifySession = async (requiredRole)=>{\n    try {\n        const supabase = createServerSupabase();\n        const { data: { session }, error } = await supabase.auth.getSession();\n        if (error) {\n            console.error(\"Error getting session:\", error);\n            return null;\n        }\n        if (!session) {\n            return null;\n        }\n        // If a specific role is required, check user role\n        if (requiredRole) {\n            const { data: user, error: userError } = await supabase.from(\"users\").select(\"role\").eq(\"id\", session.user.id).single();\n            if (userError) {\n                console.error(\"Error getting user role:\", userError);\n                return null;\n            }\n            if (!user || user.role !== requiredRole) {\n                return null;\n            }\n        }\n        return session;\n    } catch (error) {\n        console.error(\"Error verifying session:\", error);\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase-server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbookings%2Froute&page=%2Fapi%2Fbookings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbookings%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();